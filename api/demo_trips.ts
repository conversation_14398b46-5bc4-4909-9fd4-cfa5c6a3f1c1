// Demo Trips API - Static JSON Data Source
import demoTripsData from '../data/demo_trips.json';

// TypeScript Interfaces for Demo Trip Data Structure
export interface DemoTripDriver {
  driverId: string;
  driverName: string;
  driverPassportNumber: string;
  driverNationality: string;
  driverContactNo?: string;
}

export interface DemoTripVehicle {
  vehicleId: string;
  vehiclePlateNumber: string;
  trackerNo: string; // Renamed from trackerId to match screenshot
  model?: string;
  color?: string;
  type?: string;
  plateCountry?: string;
}

export interface DemoTripRoute {
  routeId: string;
  routeName: string;
  entryPort: string;
  exitPort: string;
}

export interface DemoTripShipment {
  shipmentId: string;
  shipmentDescription: string;
  ownerDescription?: string;
  shipmentDescriptionArabic?: string; // وصف الشحنة المدخل
}

export interface DemoTripLocation {
  latitude: number;
  longitude: number;
  timestamp: string;
}

export interface DemoTripTracking {
  currentLocation: DemoTripLocation;
  completeDistance: number; // Number for easy conversion, display format handled in UI
  remainingDistance: number; // Number for easy conversion, display format handled in UI
  estimatedArrival: string | null;
  elocks?: string;
}

export interface DemoTripCompliance {
  securityNotes: string;
  customsStatus: 'pending' | 'cleared' | 'rejected';
  documentStatus: 'pending' | 'complete' | 'incomplete';
}

export interface DemoTrip {
  tripId: string;
  transitNumber: string; // Renamed from tripNumber to match screenshot
  tripStatus: 'pending' | 'activated' | 'ended' | 'cancelled';
  transitType: string; // Renamed from tripType and changed to string to match screenshot
  creationDate: string;
  activationDate: string | null;
  completionDate: string | null;
  transitSeqNo?: string;
  declarationDate?: string;
  startingDate?: string;
  expectedArrivalDate?: string;
  endDate?: string;
  driver: DemoTripDriver;
  vehicle: DemoTripVehicle;
  route: DemoTripRoute;
  shipment: DemoTripShipment;
  tracking: DemoTripTracking;
  compliance: DemoTripCompliance;
}

export interface DemoTripsResponse {
  trips: DemoTrip[];
  total: number;
  page: number;
  limit: number;
}

// API Functions
export async function getDemoTrips(
  page: number = 1,
  limit: number = 10,
  status?: string,
  type?: string
): Promise<DemoTripsResponse> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  let filteredTrips = demoTripsData.trips;

  // Apply filters
  if (status && status !== 'all') {
    filteredTrips = filteredTrips.filter(trip => trip.tripStatus === status);
  }

  if (type && type !== 'all') {
    filteredTrips = filteredTrips.filter(trip => trip.transitType === type);
  }

  // Apply pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedTrips = filteredTrips.slice(startIndex, endIndex);

  return {
    trips: paginatedTrips,
    total: filteredTrips.length,
    page,
    limit
  };
}

export async function getDemoTripById(tripId: string): Promise<DemoTrip | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  const trip = demoTripsData.trips.find(trip => trip.tripId === tripId);
  return trip || null;
}

// Helper functions for UI
export function getTripStatusIcon(status: DemoTrip['tripStatus']): string {
  switch (status) {
    case 'pending':
      return '⏳'; // Hourglass
    case 'activated':
      return '🚛'; // Truck
    case 'ended':
      return '✅'; // Check mark
    case 'cancelled':
      return '❌'; // X mark
    default:
      return '❓'; // Question mark
  }
}

export function getTripStatusColor(status: DemoTrip['tripStatus']): string {
  switch (status) {
    case 'pending':
      return 'text-yellow-600 bg-yellow-50';
    case 'activated':
      return 'text-blue-600 bg-blue-50';
    case 'ended':
      return 'text-green-600 bg-green-50';
    case 'cancelled':
      return 'text-red-600 bg-red-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
}

export function getTripTypeIcon(type: DemoTrip['tripType']): string {
  switch (type) {
    case 'import':
      return '📥'; // Inbox
    case 'export':
      return '📤'; // Outbox
    case 'transit':
      return '🔄'; // Arrows
    default:
      return '📦'; // Package
  }
}

export function formatTripDate(dateString: string | null): string {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

export function calculateTripProgress(trip: DemoTrip): number {
  const completed = trip.tracking.completeDistance || 0;
  const remaining = trip.tracking.remainingDistance || 0;

  const total = completed + remaining;
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

// Helper function to format distance for display
export function formatDistance(distance: number): string {
  return `${distance}kilometers`;
}
