{"locations": [{"id": 1, "name": "Ahmed Test Location 1", "nameAr": "موق<PERSON> أحمد التجريبي 1", "lat": 24.7136, "lng": 46.6753, "type": "airport", "description": "Test airport location for Ahmed", "descriptionAr": "موقع مطار تجريبي لأحمد", "status": "active", "facilities": ["testing", "demo", "airport", "cargo"]}, {"id": 2, "name": "Ahmed Test Seaport", "nameAr": "مين<PERSON><PERSON> أحمد التجريبي", "lat": 21.4858, "lng": 39.1925, "type": "seaport", "description": "Test seaport for <PERSON>'s demo", "descriptionAr": "ميناء تجريبي لعرض أحمد", "status": "active", "facilities": ["testing", "demo", "seaport", "container"]}, {"id": 3, "name": "Ahmed Checkpoint Alpha", "nameAr": "نقطة تفتيش أحمد ألفا", "lat": 26.2361, "lng": 50.15, "type": "checkpoint", "description": "Test checkpoint for <PERSON>", "descriptionAr": "نقطة تفتيش تجريبية لأحمد", "status": "active", "facilities": ["testing", "demo", "checkpoint", "border"]}, {"id": 4, "name": "Ahmed Police Station", "nameAr": "مركز شرطة أحمد", "lat": 25.0, "lng": 47.0, "type": "police_station", "description": "Test police station for Ahmed", "descriptionAr": "مركز شرطة تجريبي لأحمد", "status": "active", "facilities": ["testing", "demo", "police", "security"]}, {"id": 5, "name": "Ahmed <PERSON>port Beta", "nameAr": "مين<PERSON><PERSON> أحمد البري بيتا", "lat": 28.4292, "lng": 48.4917, "type": "landport", "description": "Test landport for <PERSON>'s testing", "descriptionAr": "ميناء بري تجريبي لاختبار أحمد", "status": "active", "facilities": ["testing", "demo", "landport", "cargo"]}], "alerts": [{"id": 1, "title": "Ahmed Test Alert 1", "titleAr": "تنبيه أحمد التجريبي 1", "description": "This is a test alert for <PERSON>'s demo", "descriptionAr": "هذا تنبيه تجريبي لعرض أحمد", "time": "2 mins ago", "timeAr": "منذ دقيقتين", "severity": "high", "type": "test", "vehicleId": "AHMED001", "location": {"lat": 24.7136, "lng": 46.6753, "address": "Ahmed Test Location 1"}, "isRead": false, "timestamp": "2024-01-15T11:28:00Z"}, {"id": 2, "title": "<PERSON>", "titleAr": "تنبيه عرض أحمد", "description": "Demo alert for testing purposes", "descriptionAr": "تنبيه عرض لأغراض الاختبار", "time": "5 mins ago", "timeAr": "منذ 5 دقائق", "severity": "medium", "type": "demo", "vehicleId": "AHMED002", "location": {"lat": 21.4858, "lng": 39.1925, "address": "Ahmed Test Seaport"}, "isRead": false, "timestamp": "2024-01-15T11:25:00Z"}, {"id": 3, "title": "Ahmed System Test", "titleAr": "اختبار نظام أحمد", "description": "System test alert for <PERSON>", "descriptionAr": "تنبيه اختبار النظام لأحمد", "time": "10 mins ago", "timeAr": "منذ 10 دقائق", "severity": "low", "type": "system", "vehicleId": "AHMED003", "location": {"lat": 26.2361, "lng": 50.15, "address": "Ahmed Checkpoint Alpha"}, "isRead": true, "timestamp": "2024-01-15T11:20:00Z"}], "routes": [{"id": 1, "name": "Ahmed Test Route 1", "nameAr": "طريق أحمد التجريبي 1", "startLocation": {"id": 1, "name": "Ahmed Test Location 1", "lat": 24.7136, "lng": 46.6753}, "endLocation": {"id": 2, "name": "Ahmed Test Seaport", "lat": 21.4858, "lng": 39.1925}, "distance": 950, "estimatedDuration": 480, "status": "active", "type": "test", "waypoints": [{"lat": 23.0, "lng": 43.0, "name": "Ahmed Waypoint 1"}], "isUserRoute": true}, {"id": 2, "name": "<PERSON>", "nameAr": "طريق عرض أحمد", "startLocation": {"id": 3, "name": "Ahmed Checkpoint Alpha", "lat": 26.2361, "lng": 50.15}, "endLocation": {"id": 4, "name": "Ahmed Police Station", "lat": 25.0, "lng": 47.0}, "distance": 420, "estimatedDuration": 300, "status": "active", "type": "demo", "waypoints": [], "isUserRoute": true}], "statistics": {"pieData": [{"name": "Ahmed Test Completed", "nameAr": "اختبارا<PERSON> أحمد المكتملة", "value": 15, "color": "#22c55e", "percentage": 75.0}, {"name": "Ahmed Test Pending", "nameAr": "اختبا<PERSON><PERSON><PERSON> أحمد المعلقة", "value": 5, "color": "#f59e0b", "percentage": 25.0}], "barData": [{"name": "Ahmed Test Analytics", "nameAr": "تحليل اختبارات أحمد", "activeTrips": 8, "activeTripsWithAlerts": 2, "activeTripsWithCommunicationLost": 0, "completedTrips": 25, "delayedTrips": 1, "onTimeTrips": 24}], "summary": {"totalActiveTrips": 8, "totalCompletedTrips": 25, "totalAlerts": 2, "communicationLostTrips": 0, "averageDelay": 5, "onTimePercentage": 96.0, "lastUpdated": "2024-01-15T11:30:00Z"}}}