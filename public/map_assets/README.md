# Map Assets Icons

This directory contains SVG icons for use in the transit tracking system map interface.

## Available Icons

### Vehicle Icons
- **truck-icon.svg** - Standard truck/vehicle icon for marking vehicles on the map
- **vehicle-tracking.svg** - Active vehicle tracking icon with circular border for monitored vehicles

### Location Markers
- **location-pin.svg** - Standard location pin for general locations
- **port-icon.svg** - Port/warehouse icon for marking port facilities
- **customs-icon.svg** - Customs checkpoint icon for border crossings

### Route Markers
- **start-point.svg** - Green checkmark icon for trip starting points
- **end-point.svg** - Red square icon for trip destinations
- **checkpoint-icon.svg** - Purple waypoint icon for intermediate stops
- **route-icon.svg** - Multi-colored route lines icon

### Alert Icons
- **alert-icon.svg** - Warning triangle icon for alerts and suspicious activities

## Usage

These icons are designed to be used in the Google Maps integration and other map components. All icons are created as SVG files for scalability and performance.

### Example Usage in React:
```jsx
<img src="/map_assets/truck-icon.svg" alt="Vehicle" width="32" height="32" />
```

### In Google Maps Markers:
```javascript
const marker = new google.maps.Marker({
  position: { lat: -34.397, lng: 150.644 },
  map: map,
  icon: {
    url: '/map_assets/truck-icon.svg',
    scaledSize: new google.maps.Size(32, 32)
  }
});
```

## Icon Specifications

- Format: SVG
- Typical size: 24x24 to 32x32 pixels
- Colors: Consistent with the application's color scheme
- Optimized for both light and dark map themes