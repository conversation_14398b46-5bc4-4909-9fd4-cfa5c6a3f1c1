#!/usr/bin/env python3
"""
Convert Markdown with Mermaid diagrams to PDF
This script extracts Mermaid diagrams, converts them to images, and creates a PDF
"""

# python3 convert_md_to_pdf.py

import re
import os
import subprocess
import tempfile
import sys

def extract_mermaid_diagrams(content):
    """Extract Mermaid diagrams from markdown content"""
    pattern = r'```mermaid\n(.*?)\n```'
    diagrams = re.findall(pattern, content, re.DOTALL)
    return diagrams

def create_mermaid_image(diagram_content, output_path):
    """Create an image from Mermaid diagram content"""
    # Create a temporary mermaid file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.mmd', delete=False) as f:
        f.write(diagram_content)
        mermaid_file = f.name
    
    try:
        # Use local mermaid-cli to convert to PNG
        cmd = [
            './node_modules/.bin/mmdc',
            '-i', mermaid_file,
            '-o', output_path,
            '-t', 'neutral',
            '-b', 'white'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error converting Mermaid diagram: {result.stderr}")
            return False
        return True
    finally:
        # Clean up temporary file
        os.unlink(mermaid_file)

def replace_mermaid_with_images(content, image_dir):
    """Replace Mermaid code blocks with image references"""
    diagrams = extract_mermaid_diagrams(content)
    modified_content = content
    
    for i, diagram in enumerate(diagrams):
        image_filename = f"mermaid_diagram_{i+1}.png"
        image_path = os.path.join(image_dir, image_filename)
        
        # Create the image
        if create_mermaid_image(diagram, image_path):
            # Replace the mermaid block with an image reference
            mermaid_block = f"```mermaid\n{diagram}\n```"
            image_markdown = f"![Diagram {i+1}]({image_path})"
            modified_content = modified_content.replace(mermaid_block, image_markdown, 1)
        else:
            print(f"Failed to create image for diagram {i+1}")
    
    return modified_content

def convert_to_pdf(input_file, output_file):
    """Convert markdown to PDF using pandoc"""
    # Create temporary directory for images
    with tempfile.TemporaryDirectory() as temp_dir:
        # Read the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace Mermaid diagrams with images
        modified_content = replace_mermaid_with_images(content, temp_dir)
        
        # Create temporary markdown file with images
        temp_md_file = os.path.join(temp_dir, 'temp_with_images.md')
        with open(temp_md_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # Convert to PDF using pandoc
        cmd = [
            'pandoc',
            temp_md_file,
            '-o', output_file,
            '--pdf-engine=xelatex',
            '--toc',
            '--toc-depth=3',
            '-V', 'geometry:margin=1in',
            '-V', 'fontsize=11pt',
            '-V', 'documentclass=article'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error converting to PDF: {result.stderr}")
            return False
        
        print(f"Successfully converted {input_file} to {output_file}")
        return True

def main():
    input_file = "_docs_implementation/00_tts_architecture_overview.md"
    output_file = "_docs_implementation/00_tts_architecture_overview.pdf"
    
    if not os.path.exists(input_file):
        print(f"Input file {input_file} not found!")
        sys.exit(1)
    
    if convert_to_pdf(input_file, output_file):
        print("Conversion completed successfully!")
    else:
        print("Conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
