"use client";

import { useEffect, useState } from "react";
import { DataTable } from "@/components/shared/DataTable";
import { AlertCircleIcon, BatteryIcon, CheckIcon, WifiCogIcon, Signal, Plug } from "lucide-react";
import { TripAlert } from "@/types/trip_alert";
import TripMasterDetailView from "@/components/TripMasterDetailView";

export default function AlertListView() {
  const [alerts, setAlerts] = useState<TripAlert[]>([]);

useEffect(() => {
  fetch("/data/trip_alerts.json")
    .then((res) => res.json())
    .then((data) => {
      if (Array.isArray(data.tripAlerts)) {
        setAlerts(data.tripAlerts);
      } else {
        console.error("Fetched data does not contain a valid 'tripAlerts' array:", data);
        setAlerts([]);
      }
    })
    .catch((error) => {
      console.error("Error fetching trip alerts:", error);
      setAlerts([]);
    });
}, []);

const statusConfig: Record<
  TripAlert["alertStatus"][number],
  {
    color: string;
    icon: React.ReactNode;
  }
> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <Signal className="text-black-500" size={16} />,
  },
  acknowledged: {
    color: "blue",
    icon: <WifiCogIcon className="text-blue-500" size={16} />,
  },
  triggered: {
    color: "orange",
    icon: <Plug className="text-black-500" size={16} />,
  },
};

  return (
    <div className="bg-gray-100 min-h-screen p-6 space-y-4">
      <h1 className="text-xl font-bold text-red-600 flex items-center gap-2">
        <AlertCircleIcon className="text-red-600" /> Trip Alerts List
      </h1>

      <DataTable<TripAlert>
        keyField="alertId"
        data={alerts || []} // Ensure data is always an array
        stickyHeader={true}
        pageSize={10}
        filterable={false}
        expandable={true}
        searchable={false}
        sortable={true}
        selectable={true}
        exportable={true}
        exportFormats={["xlsx",]}
        exportFileNameKey="trip-alerts"
        columns={[
          { key: "alertId", title: "Alert ID" },
          { key: "alertType", title: "Type" },
          { key: "tripId", title: "Trip ID" ,render: (row) => (
            <span className="text-blue-400 font-medium">
              {row.transitNumber}
            </span>
          )},
          { key: "routeName", title: "Route" },
          { key: "shipmentDescription", title: "Shipment" },

          {
            key: "timestamp",
            title: "Time",
            render: (row) =>
              new Date(row.timestamp).toLocaleString("en-GB", {
                dateStyle: "short",
                timeStyle: "short",
              }),
          },
          {
            key: "location",
            title: "Address",
            render: (row) => row.location.address,
          },
           {
            key: "alert_status",
            title: "Alert Trip Status",
            render: (row) => (
              <div className="flex gap-1 items-center">
                {row.alertStatus?.map((s, idx) => (
                  <span key={idx}>{statusConfig[s]?.icon}</span>
                ))}
              </div>
            ),
          },
        ]}
        expandRowByClick={true}
      />
    </div>
  );
}
