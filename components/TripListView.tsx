"use client";

import { DataTable } from "@/components/shared/DataTable";
import TripMasterDetailView from "@/components/TripMasterDetailView";
import {
  BatteryIcon,
  CheckIcon,
  PowerOff,
  XCircle,
  Clock,
  MapPin,
  Eye,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Trip } from "@/types/trip";



const statusConfig: Record<
  string,
  {
    color: string;
    icon: React.ReactNode;
  }
> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <PowerOff className="text-red-500" size={16} />,
  },
  cancelled: {
    color: "red",
    icon: <XCircle className="text-red-500" size={16} />,
  },
  pending_review: {
    color: "orange",
    icon: <Clock className="text-orange-500" size={16} />,
  },
  tracking: {
    color: "blue",
    icon: <MapPin className="text-blue-500" size={16} />,
  },
  monitored: {
    color: "purple",
    icon: <Eye className="text-purple-500" size={16} />,
  },
};

export default function TripListView() {
  const [data, setData] = useState<Trip[]>([]);

  useEffect(() => {
    fetch("/data/trips.json")
      .then((res) => res.json())
      .then(setData)
      .catch(console.error);
  }, []);

  return (
    <div >

      <DataTable<Trip>
        keyField="id"
        data={data}
        stickyHeader={true}
        expandable={true}
        expandRowByClick={false}
        pageSize={10}
        filterable={true}
        searchable={true}
        sortable={true}
        selectable={true}
        exportable={true}
        exportFormats={["xlsx", "csv", "pdf"]}
        exportFileNameKey="trip-data"
        columns={[
          
          {
            key: "transitNumber",
            title: "Transit Number",
            render: (row) => (
              <span className="text-blue-400 font-medium">
                {row.transitNumber}
              </span>
            ),
          },
          { key: "description", title: "Shipment Description" },
          { key: "entry", title: "Entry-port - Exit-port" },
          { key: "lastSeen", title: "Last Seen" },
          { key: "tracker", title: "Tracker" },
          { key: "driver", title: "Driver Name" },
          { key: "vehicle", title: "Vehicle" },
          { key: "alerts", title: "Alerts" },
          {
            key: "status",
            title: "Trip Status",
            render: (row) => (
              <div className="flex gap-1 items-center">
                {row.status?.map((s, idx) => (
                  <span key={idx}>{statusConfig[s]?.icon}</span>
                ))}
              </div>
            ),
          },
        ]}
        expandedRowRender={(row) => <TripMasterDetailView row={row} />}
      />
    </div>
  );
}
