# TripAlertMapViewer Component

## Overview

The `TripAlertMapViewer` is a React component that provides an interactive Google Maps interface for visualizing trip alerts. It displays alerts as markers on the map with filtering capabilities, info windows, and a compact footer showing alert statistics.

## Features

- **Interactive Google Maps Integration**: Displays alerts as custom markers on Google Maps
- **Dynamic Filtering**: Filter alerts by type using visual filter buttons
- **Info Windows**: Click markers to view detailed alert information
- **Trip-based Filtering**: Filter alerts by specific trip ID
- **Responsive Layout**: Flexible layout with collapsible filter bar
- **Multilingual Support**: Supports English and Arabic languages
- **Compact Footer**: Shows alert count statistics
- **Custom Alert Icons**: Each alert type has its own SVG icon

## Props Interface

```typescript
interface TripAlertMapViewerProps {
  tripAlerts: TripAlert[];        // Array of trip alerts to display
  selectedTripId?: string;        // Optional trip ID to filter alerts
  onAlertClick?: (alert: TripAlert) => void; // Callback when alert marker is clicked
  className?: string;             // Additional CSS classes
}
```

## TripAlert Data Structure

```typescript
interface TripAlert {
  alertId: string;
  alertType: string;
  alertStatus: string;
  tripId: string;
  transitSequence: number;
  routeName: string;
  shipmentDescription: string;
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  severity: string;
  description: string;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
  comments?: string;
  relatedData: {
    speed: number;
    batteryLevel: number;
    signalStrength: number;
    geofenceId: string;
  };
}
```

## Supported Alert Types

The component supports the following alert types with corresponding icons:

- `tracker_tamper` - Tracker Tamper
- `tracker_dropped` - Tracker Dropped
- `lock_tamper` - Lock Tamper
- `lock_open` - Lock Open
- `lock_connection_lost` - Lock Connection Lost
- `tracker_battery_low` - Tracker Battery Low
- `lock_low_battery` - Lock Low Battery
- `gsm_signal_lost` - GSM Signal Lost
- `gps_signal_lost` - GPS Signal Lost
- `geofence_entry_breach` - Geofence Entry
- `geofence_exit_breach` - Geofence Exit
- `tracker_connection_lost` - Tracker Connection Lost
- `trip_distance_exceeded` - Trip Distance Exceeded
- `trip_time_exceeded` - Trip Time Exceeded
- `over_speeding` - Over Speeding
- `truck_stopped` - Truck Stopped
- `wrong_direction` - Wrong Direction
- `ping` - Ping
- `stop_car` - Stop Car
- `trip_activity` - Trip Activity
- `stopped_exceeded` - Stopped Exceeded

## Usage Examples

### Basic Usage

```tsx
import TripAlertMapViewer from './components/TripAlertMapViewer';

const MyComponent = () => {
  const alerts = [
    {
      alertId: "alert-1",
      alertType: "over_speeding",
      alertStatus: "active",
      tripId: "trip-123",
      transitSequence: 1,
      routeName: "Route A",
      shipmentDescription: "Container shipment",
      timestamp: "2024-01-15T10:30:00Z",
      location: {
        latitude: 24.7136,
        longitude: 46.6753,
        address: "Riyadh, Saudi Arabia"
      },
      severity: "high",
      description: "Vehicle exceeding speed limit",
      relatedData: {
        speed: 120,
        batteryLevel: 85,
        signalStrength: 90,
        geofenceId: "geo-1"
      }
    }
    // ... more alerts
  ];

  return (
    <TripAlertMapViewer
      tripAlerts={alerts}
      className="h-[600px]"
    />
  );
};
```

### With Trip Filtering

```tsx
const MyComponent = () => {
  const [selectedTrip, setSelectedTrip] = useState<string | undefined>();
  
  return (
    <TripAlertMapViewer
      tripAlerts={alerts}
      selectedTripId={selectedTrip}
      onAlertClick={(alert) => {
        console.log('Alert clicked:', alert);
        // Handle alert click
      }}
      className="h-[600px]"
    />
  );
};
```

### In a Page Layout

```tsx
// app/trip-alerts/page.tsx
import TripAlertMapViewer from '@/components/TripAlertMapViewer';

const TripAlertsPage = () => {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Trip Alerts Map</h1>
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <TripAlertMapViewer
          tripAlerts={tripAlerts}
          selectedTripId={selectedTripId}
          onAlertClick={handleAlertClick}
          className="h-[600px]"
        />
      </div>
    </div>
  );
};
```

## Component Architecture

### State Management

- **Filter Buttons**: Array of filter configurations with active/inactive states
- **Map Instance**: Google Maps instance and loading state
- **Markers**: Array of map markers corresponding to alerts
- **Info Windows**: Active info window state for marker details
- **Filter Bar Visibility**: Toggle state for filter bar display

### Key Hooks

- **useEffect**: Google Maps API loading and map initialization
- **useEffect**: Marker creation and cleanup when alerts change
- **useEffect**: Map center updates when filtered alerts change
- **useMemo**: Filtered alerts calculation based on active filters
- **useMemo**: Map center calculation from alert locations

### Layout Structure

```
TripAlertMapViewer
├── Filter Bar (collapsible)
│   ├── Filter Buttons (alert type icons)
│   └── Control Buttons (select all, deselect all, hide)
├── Map Section
│   ├── Show Filter Button (when filter bar hidden)
│   └── Google Maps Container
│       ├── Alert Markers
│       └── Info Windows
└── Compact Footer
    ├── Alert Count Display
    └── Trip Filter Info
```

## Dependencies

- **React**: Core framework
- **Google Maps JavaScript API**: Map rendering and interaction
- **Custom Hooks**: `useLanguage` for internationalization
- **Utils**: `loadGoogleMapsAPI` for API initialization

## Environment Requirements

- `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`: Google Maps API key
- Alert icon SVG files in `/public/map_assets/`

## Styling

- Uses Tailwind CSS for styling
- Responsive design with flexbox layout
- Custom marker icons (32x32px SVG)
- Hover effects and transitions
- Light/dark theme support through CSS variables

## Performance Considerations

- **Marker Cleanup**: Properly removes markers when component unmounts
- **Memoized Calculations**: Uses `useMemo` for expensive computations
- **Conditional Rendering**: Only renders map when API is loaded
- **Event Listener Management**: Properly adds/removes map event listeners

## Accessibility

- Keyboard navigation support for filter buttons
- Screen reader friendly button labels
- High contrast color schemes
- Focus indicators for interactive elements

## Browser Support

- Modern browsers with ES6+ support
- Google Maps JavaScript API compatibility
- SVG icon support
- CSS Grid and Flexbox support

## Troubleshooting

### Common Issues

1. **Map not loading**: Check Google Maps API key configuration
2. **Markers not appearing**: Verify alert data structure and coordinates
3. **Icons not displaying**: Ensure SVG files exist in `/public/map_assets/`
4. **Filter not working**: Check alert type matching with filter configuration

### Debug Mode

The component includes console logging for debugging:
- API loading status
- Map initialization
- Alert filtering
- Marker creation

## Future Enhancements

- Clustering for large numbers of alerts
- Custom map styles and themes
- Export functionality for alert data
- Real-time alert updates
- Advanced filtering options
- Heatmap visualization mode