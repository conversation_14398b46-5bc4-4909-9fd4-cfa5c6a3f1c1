/**
 * @fileoverview Map Sidebar Component
 * @description Interactive sidebar component for map with tabs and content panels
 *
 * @features
 * - Tabbed interface for different content types (Search, Alerts, Routes, Statistics)
 * - Click-outside-to-close functionality
 * - Responsive design that stays within map boundaries
 * - Multi-language support with dynamic translation
 * - Smooth animations and transitions
 */

"use client";

import React, { useEffect, useRef } from "react";
import { SidebarProps } from "../../types/map";
import MapSidebarTabs from "./MapSidebarTabs";
import MapSearchPanel from "./MapSearchPanel";
import MapAlertsPanel from "./MapAlertsPanel";
import MapRoutesPanel from "./MapRoutesPanel";

interface ExtendedSidebarProps extends Omit<SidebarProps, "onClose"> {
  onClose?: () => void;
}

const MapSidebar: React.FC<ExtendedSidebarProps> = ({
  isOpen,
  activeTab,
  onTabChange,
  alerts,
  routes,
  statistics,
  language = "en",
  onAlertClick,
  onRouteSelect,
  onClose,
}) => {
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close sidebar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node) &&
        onClose
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const isRTL = language === "ar";

  return (
    <div
      ref={sidebarRef}
      className={`absolute bg-white shadow-xl border-l w-96 overflow-y-auto z-[999] ${
        isRTL ? "left-0 border-r border-l-0" : "right-0"
      }`}
      style={{
        top: 0,
        bottom: 0,
        height: "100%",
      }}
    >
      <div className="h-full flex flex-col relative">
        {/* Tab Navigation */}
        <MapSidebarTabs
          activeTab={activeTab}
          onTabChange={onTabChange}
          language={language}
        />

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === "search" && (
            <MapSearchPanel statistics={statistics} language={language} />
          )}

          {activeTab === "alerts" && (
            <MapAlertsPanel
              alerts={alerts}
              language={language}
              onAlertClick={onAlertClick}
            />
          )}

          {activeTab === "routes" && (
            <MapRoutesPanel
              routes={routes}
              language={language}
              onRouteSelect={onRouteSelect}
            />
          )}
        </div>

        {/* Close Button */}
        <div className="border-t bg-white">
          <button
            onClick={onClose}
            className="w-full bg-red-500 text-white px-4 py-3 hover:bg-red-600 transition-colors font-medium"
          >
            {language === "ar" ? "إغلاق الشريط الجانبي" : "Close Sidebar"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MapSidebar;
