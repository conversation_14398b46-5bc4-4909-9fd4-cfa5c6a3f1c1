# Interactive Map Components Documentation

## Overview

The Interactive Map system is a comprehensive Google Maps integration built with React and TypeScript. It provides a full-featured mapping solution with real-time data visualization, interactive controls, and multi-language support.

## Architecture Overview

```
InteractiveMapContainer (Main Orchestrator)
├── GoogleMapView (Core Map Display)
├── MapControlButtons (Map Controls)
├── MapSidebar (Information Panel)
│   ├── MapSidebarTabs (Tab Navigation)
│   ├── MapSearchPanel (Search & Statistics)
│   │   └── MapStatisticsCharts (Data Visualization)
│   │       ├── MapPieChart (Pie Charts)
│   │       └── MapBarChart (Bar Charts)
│   ├── MapAlertsPanel (Alert Management)
│   └── MapRoutesPanel (Route Planning)
├── MapLoadingScreen (Loading State)
└── NoSSR (Client-Side Rendering Wrapper)
```

## Component Hierarchy

### 1. InteractiveMapContainer.tsx

**Main orchestrator component that manages all sub-components and global state**

**Responsibilities:**

- Data loading and management based on page type
- Language detection and context management
- State coordination between all child components
- Error handling and loading states
- Event handler orchestration

**Key Features:**

- Dynamic data loading (`location-monitor`, `focused-trips`, `dummy-ahmed`)
- Auto-language detection from site settings
- Hydration safety with NoSSR wrapper
- Comprehensive error boundaries

**Props Interface:**

```typescript
interface InteractiveMapProps {
  locations?: Location[];
  alerts?: Alert[];
  routes?: Route[];
  statistics?: TripStatistics;
  pageType?: "default" | "location-monitor" | "focused-trips" | "dummy-ahmed";
  initialCenter?: MapCenter;
  initialZoom?: number;
  showSidebar?: boolean;
  language?: "en" | "ar";
  onLocationClick?: (location: Location) => void;
  onAlertClick?: (alert: Alert) => void;
  onRouteSelect?: (routes: Route[]) => void;
}
```

### 2. GoogleMapView.tsx

**Core Google Maps display component with interactive markers**

**Responsibilities:**

- Google Maps API integration and initialization
- Custom SVG marker creation and management
- Info window display and interaction
- Map event handling (clicks, marker interactions)
- Performance optimization with marker caching

**Key Features:**

- Dynamic marker icons based on location type (airport, seaport, checkpoint, etc.)
- Custom SVG markers with color coding
- Interactive info windows with location details
- Responsive marker sizing (48x48px standard)
- Automatic map cleanup and memory management

**Marker Types:**

- `airport`: Blue airplane icon
- `seaport`: Teal ship icon
- `landport`: Green truck icon
- `checkpoint`: Orange shield icon
- `police_station`: Red badge icon

### 3. MapControlButtons.tsx

**Map control interface for user interactions**

**Responsibilities:**

- Sidebar toggle functionality
- Map view reset controls
- Fullscreen mode toggle
- Responsive button positioning

**Features:**

- Dynamic positioning based on sidebar state
- RTL/LTR layout support
- Accessibility-compliant controls
- Smooth animations and transitions

### 4. MapSidebar.tsx

**Main information panel with tabbed interface**

**Responsibilities:**

- Tab navigation management
- Content area rendering
- Responsive design for mobile/desktop
- Outside-click detection for mobile closure

**Features:**

- Three main tabs: Search, Alerts, Routes
- Responsive width (384px desktop, full-width mobile)
- Smooth slide animations
- RTL/LTR layout adaptation

#### 4.1 MapSidebarTabs.tsx

**Tab navigation component**

**Features:**

- Active tab highlighting
- Badge notifications for alerts
- Responsive tab sizing
- Keyboard navigation support

#### 4.2 MapSearchPanel.tsx

**Search functionality and statistics display**

**Responsibilities:**

- Location search with real-time filtering
- Statistics dashboard integration
- Search result management
- Data visualization coordination

**Features:**

- Debounced search input
- Location type filtering
- Integrated statistics charts
- Search history management

#### 4.3 MapAlertsPanel.tsx

**Real-time alert management**

**Responsibilities:**

- Alert list display and filtering
- Severity-based color coding
- Read/unread state management
- Alert interaction handling

**Features:**

- Severity levels: high (red), medium (yellow), low (blue)
- Time-based sorting
- Mark as read functionality
- Alert detail expansion

#### 4.4 MapRoutesPanel.tsx

**Route planning and management**

**Responsibilities:**

- Route list display
- Route filtering (My Routes vs All Routes)
- Route selection and management
- Distance and duration display

**Features:**

- User route filtering
- Multi-route selection
- Route details display
- Search functionality

### 5. Data Visualization Components

#### 5.1 MapStatisticsCharts.tsx

**Main statistics container**

**Responsibilities:**

- Chart layout management
- Data validation and safety checks
- Responsive chart sizing
- Multi-language label support

#### 5.2 MapPieChart.tsx

**Pie chart visualization using Recharts**

**Features:**

- Custom color schemes
- Percentage calculations
- Responsive sizing
- Tooltip integration
- Legend display

#### 5.3 MapBarChart.tsx

**Bar chart visualization using Recharts**

**Features:**

- Multi-series data support
- Custom color coding
- Responsive axes
- Data label formatting
- Grid line customization

### 6. Utility Components

#### 6.1 MapLoadingScreen.tsx

**Loading state display**

**Features:**

- Animated loading spinner
- Multi-language loading messages
- Consistent styling with app theme
- Responsive design

#### 6.2 NoSSR.tsx

**Client-side rendering wrapper**

**Purpose:**

- Prevents hydration mismatches
- Ensures client-only rendering for map components
- Provides fallback loading state
- Handles SSR compatibility issues

## Data Flow Architecture

### 1. Data Loading Pipeline

```
usePageData Hook → JSON Data Files → Component Props → UI Rendering
```

### 2. State Management

```
InteractiveMapContainer (Global State)
├── Language Context (useLanguage)
├── Sidebar State (useSidebarState)
├── Map State (Google Maps API)
└── Data State (usePageData)
```

### 3. Event Flow

```
User Interaction → Event Handler → State Update → UI Re-render
```

## Integration Points

### Google Maps API

- Environment variable: `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`
- Dynamic script loading with error handling
- Marker management and optimization
- Map controls and styling

### Data Sources

- `location-monitor-data.json`: Security monitoring data
- `focused-trips-data.json`: Priority trip management
- `dummy-ahmed-data.json`: Test data for development
- Default data files: `locations.json`, `alerts.json`, `routes.json`, `statistics.json`

### Language Support

- Arabic (RTL) and English (LTR) layouts
- Dynamic text direction handling
- Translated labels and messages
- Cultural number formatting

## Performance Optimizations

### 1. Marker Management

- Marker instance caching
- Efficient marker cleanup
- Optimized re-rendering
- Memory leak prevention

### 2. Data Loading

- Page-specific data loading
- Data caching mechanisms
- Lazy loading for large datasets
- Error boundary protection

### 3. Rendering Optimization

- NoSSR wrapper for hydration safety
- Conditional rendering for performance
- Debounced search inputs
- Optimized re-renders with useMemo/useCallback

## Error Handling

### 1. Google Maps API Errors

- API key validation
- Network error handling
- Fallback loading states
- User-friendly error messages

### 2. Data Validation

- Type safety with TypeScript
- Runtime data validation
- Fallback default values
- Graceful degradation

### 3. Hydration Safety

- Client-side only rendering for maps
- Consistent server/client state
- Fallback loading screens
- Error boundary recovery

## Usage Examples

### Basic Implementation

```tsx
<InteractiveMapContainer
  pageType="location-monitor"
  initialCenter={{ lat: 24.7136, lng: 46.6753 }}
  initialZoom={6}
  showSidebar={true}
  onLocationClick={(location) => console.log(location)}
/>
```

### Custom Data Implementation

```tsx
<InteractiveMapContainer
  locations={customLocations}
  alerts={customAlerts}
  routes={customRoutes}
  statistics={customStatistics}
  language="ar"
  onAlertClick={handleAlertClick}
/>
```

## Development Guidelines

### 1. Adding New Location Types

1. Update marker icon configuration in `GoogleMapView.tsx`
2. Add type definition in `types/map.ts`
3. Update data files with new location type
4. Test marker rendering and interactions

### 2. Adding New Chart Types

1. Create new chart component in the same directory
2. Import and integrate in `MapStatisticsCharts.tsx`
3. Update data interfaces in `types/map.ts`
4. Add responsive styling and language support

### 3. Extending Data Sources

1. Add new data file in `/data` directory
2. Update `usePageData.ts` hook with new page type
3. Add type definitions and validation
4. Update component props and interfaces

## Technical Implementation Details

### Custom Marker System

The marker system uses SVG-based icons with dynamic coloring:

```typescript
const createMarkerIcon = (type: string) => {
  const iconConfigs = {
    airport: {
      color: "#1e40af",
      svg: `<svg>...</svg>`, // Airplane icon
    },
    seaport: {
      color: "#0891b2",
      svg: `<svg>...</svg>`, // Ship icon
    },
    // ... other types
  };

  return {
    url:
      "data:image/svg+xml;charset=UTF-8," +
      encodeURIComponent(`
      <svg width="48" height="48" viewBox="0 0 48 48">
        <circle cx="24" cy="24" r="22" fill="${config.color}"/>
        ${config.svg}
      </svg>
    `),
    scaledSize: new google.maps.Size(48, 48),
    anchor: new google.maps.Point(24, 24),
  };
};
```

### Data Structure Examples

#### Location Object

```typescript
interface Location {
  id: number;
  name: string;
  nameAr: string;
  lat: number;
  lng: number;
  type: "airport" | "seaport" | "landport" | "checkpoint" | "police_station";
  description: string;
  descriptionAr: string;
  status: "active" | "inactive" | "maintenance";
  facilities: string[];
}
```

#### Alert Object

```typescript
interface Alert {
  id: number;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  time: string;
  timeAr: string;
  severity: "high" | "medium" | "low";
  type: string;
  vehicleId: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  isRead: boolean;
  timestamp: string;
}
```

### State Management Patterns

#### Global State Flow

```typescript
// Main container manages all state
const InteractiveMapContainer = () => {
  const { language, isHydrated } = useLanguage();
  const { locations, alerts, routes, statistics } = usePageData(pageType);
  const { isOpen, activeTab, setIsOpen, setActiveTab } = useSidebarState();

  // Coordinate all child components
  return (
    <NoSSR fallback={<MapLoadingScreen />}>
      <GoogleMapView locations={locations} />
      <MapControlButtons onToggleSidebar={() => setIsOpen(!isOpen)} />
      <MapSidebar
        isOpen={isOpen}
        activeTab={activeTab}
        alerts={alerts}
        routes={routes}
        statistics={statistics}
      />
    </NoSSR>
  );
};
```

### Responsive Design Implementation

#### Breakpoint System

- **Mobile**: < 768px (full-width sidebar)
- **Tablet**: 768px - 1024px (overlay sidebar)
- **Desktop**: > 1024px (side-by-side layout)

#### CSS Classes Used

```css
/* Sidebar responsive classes */
.sidebar-mobile: "fixed inset-0 z-50 bg-white"
.sidebar-desktop: "relative w-96 bg-white border-l"

/* Map container responsive */
.map-container: "relative w-full h-full"
.map-with-sidebar: "w-full lg:w-[calc(100%-384px)]"
```

### Performance Monitoring

#### Key Metrics to Track

1. **Map Load Time**: Time from component mount to map ready
2. **Marker Render Time**: Time to render all location markers
3. **Data Fetch Time**: Time to load page-specific data
4. **Memory Usage**: Monitor for memory leaks in marker management

#### Optimization Techniques

```typescript
// Marker cleanup to prevent memory leaks
useEffect(() => {
  return () => {
    markers.forEach((marker) => {
      google.maps.event.clearInstanceListeners(marker);
      marker.setMap(null);
    });
  };
}, [markers]);

// Debounced search to reduce API calls
const debouncedSearch = useMemo(
  () =>
    debounce((term: string) => {
      // Search implementation
    }, 300),
  []
);
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Map Not Loading

**Symptoms**: Blank map area, loading spinner persists
**Solutions**:

- Verify Google Maps API key in `.env.local`
- Check browser console for API errors
- Ensure billing is enabled for Google Maps API
- Verify domain restrictions in Google Cloud Console

#### 2. Hydration Errors

**Symptoms**: Console warnings about server/client mismatch
**Solutions**:

- Ensure NoSSR wrapper is properly implemented
- Check that client-only code is properly guarded
- Verify localStorage access is wrapped in `typeof window !== 'undefined'`

#### 3. Markers Not Displaying

**Symptoms**: Map loads but no location markers appear
**Solutions**:

- Verify location data structure matches interface
- Check marker icon SVG syntax
- Ensure lat/lng coordinates are valid numbers
- Verify marker creation logic in GoogleMapView

#### 4. Sidebar Not Responsive

**Symptoms**: Sidebar doesn't adapt to screen size
**Solutions**:

- Check Tailwind CSS classes for responsive breakpoints
- Verify sidebar state management
- Test touch events on mobile devices
- Check z-index conflicts

### Debug Mode Implementation

```typescript
// Add to InteractiveMapContainer for debugging
const DEBUG_MODE = process.env.NODE_ENV === "development";

if (DEBUG_MODE) {
  console.log("Map Data:", { locations, alerts, routes, statistics });
  console.log("Map State:", { isHydrated, language, sidebarOpen });
}
```

This comprehensive documentation covers all aspects of the Interactive Map system implementation and usage.
