'use client';

import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { BarChartData } from '../../types/map';

interface MapBarChartProps {
  data: BarChartData[];
  language?: 'en' | 'ar';
}

// Custom tooltip component moved outside
const CustomTooltip = ({ active, payload, label, language }: any) => {
  if (active && payload?.length) {
    return (
      <div className="bg-white p-3 border rounded shadow-lg">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p
            key={`tooltip-${entry.dataKey}-${index}`}
            className="text-sm"
            style={{ color: entry.color }}
          >
            {entry.dataKey}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const MapBarChart: React.FC<MapBarChartProps> = ({
  data,
  language = 'en'
}) => {
  // Transform data for recharts
  const chartData = data.map(item => ({
    name: language === 'ar' ? item.nameAr : item.name,
    'Active Trips': item.activeTrips,
    'Active Trips With Alerts': item.activeTripsWithAlerts,
    'Active Trips With Communication Lost': item.activeTripsWithCommunicationLost
  }));

  const barLabels = {
    'Active Trips': language === 'ar' ? 'الرحلات النشطة' : 'Active Trips',
    'Active Trips With Alerts': language === 'ar' ? 'الرحلات النشطة مع التنبيهات' : 'Active Trips With Alerts',
    'Active Trips With Communication Lost': language === 'ar' ? 'الرحلات النشطة مع فقدان الاتصال' : 'Active Trips With Communication Lost'
  };

  return (
    <div className="space-y-4">
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="name"
              axisLine={true}
              tickLine={true}
              fontSize={12}
            />
            <YAxis
              domain={[0, 1600]}
              ticks={[0, 200, 400, 600, 800, 1000, 1200, 1400, 1600]}
              axisLine={true}
              tickLine={true}
              fontSize={12}
            />
            <Tooltip content={(props) => <CustomTooltip {...props} language={language} />} />
            <Bar 
              dataKey="Active Trips" 
              fill="#dc2626" 
              name={barLabels['Active Trips']}
            />
            <Bar 
              dataKey="Active Trips With Alerts" 
              fill="#3b82f6" 
              name={barLabels['Active Trips With Alerts']}
            />
            <Bar 
              dataKey="Active Trips With Communication Lost" 
              fill="#22c55e" 
              name={barLabels['Active Trips With Communication Lost']}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Custom Legend */}
      <div className="space-y-1 text-xs">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-600"></div>
            <span>{barLabels['Active Trips']}</span>
          </div>
          <span className="font-medium">
            {data[0]?.activeTrips || 0}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500"></div>
            <span>{barLabels['Active Trips With Alerts']}</span>
          </div>
          <span className="font-medium">
            {data[0]?.activeTripsWithAlerts || 0}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500"></div>
            <span>{barLabels['Active Trips With Communication Lost']}</span>
          </div>
          <span className="font-medium">
            {data[0]?.activeTripsWithCommunicationLost || 0}
          </span>
        </div>
      </div>
    </div>
  );
};

export default MapBarChart;
