/**
 * @fileoverview Map Control Buttons Component
 * @description Control buttons for map interactions including sidebar toggle, reset view, and fullscreen
 *
 * @features
 * - Sidebar toggle button with directional icons based on language
 * - Reset view button to return to initial map position
 * - Fullscreen toggle button for immersive map experience
 * - RTL support with appropriate icon directions
 * - Responsive positioning and hover effects
 * - Accessibility features with proper titles and ARIA labels
 */

"use client";

import React from "react";
import { ChevronLeft, ChevronRight, Maximize2, RotateCcw } from "lucide-react";

interface MapControlButtonsProps {
  sidebarOpen: boolean;
  onToggleSidebar: () => void;
  onResetView?: () => void;
  onFullscreen?: () => void;
  language?: "en" | "ar";
}

const MapControlButtons: React.FC<MapControlButtonsProps> = ({
  sidebarOpen,
  onToggleSidebar,
  onResetView,
  onFullscreen,
  language = "en",
}) => {
  const isRTL = language === "ar";

  return (
    <>
      {/* Sidebar Toggle Button */}
      <button
        onClick={onToggleSidebar}
        className={`fixed top-1/2 transform -translate-y-1/2 bg-blue-600 text-white rounded-full shadow-xl border-2 border-white p-4 hover:bg-blue-700 transition-all duration-200 z-[1001] ${
          isRTL ? "left-2" : "right-2"
        }`}
        title={language === "ar" ? "تبديل الشريط الجانبي" : "Toggle Sidebar"}
      >
        {sidebarOpen ? (
          isRTL ? (
            <ChevronLeft className="w-6 h-6" />
          ) : (
            <ChevronRight className="w-6 h-6" />
          )
        ) : isRTL ? (
          <ChevronRight className="w-6 h-6" />
        ) : (
          <ChevronLeft className="w-6 h-6" />
        )}
      </button>

      {/* Additional Map Controls */}
      <div
        className={`absolute top-4 flex flex-col space-y-2 z-[1000] ${
          isRTL ? "right-4" : "left-4"
        }`}
      >
        {/* Reset View Button */}
        {onResetView && (
          <button
            onClick={onResetView}
            className="bg-white text-gray-700 rounded-lg shadow-md border p-3 hover:bg-gray-50 transition-colors"
            title={language === "ar" ? "إعادة تعيين العرض" : "Reset View"}
          >
            <RotateCcw className="w-5 h-5" />
          </button>
        )}

        {/* Fullscreen Button */}
        {onFullscreen && (
          <button
            onClick={onFullscreen}
            className="bg-white text-gray-700 rounded-lg shadow-md border p-3 hover:bg-gray-50 transition-colors"
            title={language === "ar" ? "ملء الشاشة" : "Fullscreen"}
          >
            <Maximize2 className="w-5 h-5" />
          </button>
        )}
      </div>
    </>
  );
};

export default MapControlButtons;
