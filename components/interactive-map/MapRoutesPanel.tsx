'use client';

import React, { useState } from 'react';
import { Route } from '../../types/map';
import { useRouteSelection } from '../../hooks/useSidebarState';

interface MapRoutesPanelProps {
  routes: Route[];
  language?: 'en' | 'ar';
  onRouteSelect?: (routes: Route[]) => void;
}

const MapRoutesPanel: React.FC<MapRoutesPanelProps> = ({
  routes,
  language = 'en',
  onRouteSelect
}) => {
  const [routeFilter, setRouteFilter] = useState<'my-routes' | 'all'>('my-routes');
  const {
    selectedRoutes,
    searchTerm,
    showSuggestions,
    setSearchTerm,
    setShowSuggestions,
    addRoute,
    removeRoute,
    setRoutes
  } = useRouteSelection();

  // Get user routes and all route names
  const userRoutes = routes.filter(route => route.isUserRoute).map(route => route.name);
  const allRouteNames = routes.map(route => route.name);

  // Filter routes for suggestions
  const filteredRoutes = allRouteNames.filter(routeName =>
    routeName.toLowerCase().includes(searchTerm.toLowerCase()) &&
    !selectedRoutes.includes(routeName)
  );

  const handleMyRoutesClick = () => {
    setRouteFilter('my-routes');
    setRoutes(userRoutes);
  };

  const handleAllRoutesClick = () => {
    setRouteFilter('all');
    setRoutes(allRouteNames);
  };

  const handleReset = () => {
    setRouteFilter('my-routes');
    setSearchTerm('');
    setRoutes([]);
  };

  const handleRouteSelection = () => {
    const selectedRouteObjects = routes.filter(route => 
      selectedRoutes.includes(route.name)
    );
    onRouteSelect?.(selectedRouteObjects);
  };

  return (
    <div className="p-4 space-y-4">
      {/* Route Filter Buttons */}
      <div className="flex w-full gap-1">
        <button
          onClick={handleMyRoutesClick}
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-sm transition-colors ${
            routeFilter === 'my-routes'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
          }`}
        >
          {language === 'ar' ? 'طرقي' : 'My Routes'}
        </button>
        <button
          onClick={handleAllRoutesClick}
          className={`flex-1 py-2 px-3 text-sm font-medium rounded-sm transition-colors ${
            routeFilter === 'all'
              ? 'bg-green-500 text-white'
              : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
          }`}
        >
          {language === 'ar' ? 'الكل' : 'All'}
        </button>
        <button
          onClick={handleReset}
          className="flex-1 py-2 px-3 text-sm font-medium bg-red-500 text-white rounded-sm hover:bg-red-600 transition-colors"
        >
          {language === 'ar' ? 'إعادة تعيين' : 'Reset'}
        </button>
      </div>

      {/* Multi-Select Route Input */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700 text-center">
          {language === 'ar' ? 'اسم الطريق' : 'Route Name'}
        </label>

        {/* Selected Routes Tags */}
        <div className="min-h-[120px] border border-gray-300 rounded p-3 bg-white">
          <div className="flex flex-wrap gap-2">
            {selectedRoutes.map((route, index) => (
              <div
                key={`${route}-${index}`}
                className="flex items-center bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm border"
              >
                <span className="mr-2 text-xs">{route}</span>
                <button
                  onClick={() => removeRoute(index)}
                  className="text-gray-500 hover:text-gray-700 font-bold text-lg leading-none"
                  aria-label={`Remove ${route}`}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Search Input */}
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => {
              const value = e.target.value;
              setSearchTerm(value);
              setShowSuggestions(value.length > 0);
            }}
            onFocus={() => {
              if (searchTerm.length > 0) {
                setShowSuggestions(true);
              }
            }}
            onBlur={() => {
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder={language === 'ar' ? 'اكتب للبحث وإضافة الطرق...' : 'Type to search and add routes...'}
            className="w-full p-2 border border-gray-300 rounded text-sm"
          />

          {/* Autocomplete Suggestions */}
          {showSuggestions && filteredRoutes.length > 0 && (
            <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto mt-1">
              {filteredRoutes.map((route, index) => (
                <button
                  key={`suggestion-${route}-${index}`}
                  onClick={() => addRoute(route)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      addRoute(route);
                    }
                  }}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 border-b border-gray-100 last:border-b-0 focus:outline-none focus:bg-gray-100"
                >
                  {route}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Apply Selection Button */}
        <button
          onClick={handleRouteSelection}
          className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors text-sm font-medium"
        >
          {language === 'ar' ? 'تطبيق التحديد' : 'Apply Selection'}
        </button>
      </div>
    </div>
  );
};

export default MapRoutesPanel;
