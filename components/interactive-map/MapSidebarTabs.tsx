'use client';

import React from 'react';
import { SidebarTab } from '../../types/map';

interface MapSidebarTabsProps {
  activeTab: SidebarTab;
  onTabChange: (tab: SidebarTab) => void;
  language?: 'en' | 'ar';
}

const MapSidebarTabs: React.FC<MapSidebarTabsProps> = ({
  activeTab,
  onTabChange,
  language = 'en'
}) => {
  const tabs: { key: SidebarTab; label: string; labelAr: string }[] = [
    { key: 'search', label: 'Search', labelAr: 'البحث' },
    { key: 'alerts', label: 'Alerts', labelAr: 'التنبيهات' },
    { key: 'routes', label: 'Routes', labelAr: 'الطرق' }
  ];

  return (
    <div className="flex border-b bg-gray-50">
      {tabs.map((tab) => (
        <button
          key={tab.key}
          onClick={() => onTabChange(tab.key)}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === tab.key
              ? 'bg-green-500 text-white'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
          }`}
        >
          {language === 'ar' ? tab.labelAr : tab.label}
        </button>
      ))}
    </div>
  );
};

export default MapSidebarTabs;
