'use client';

import { useLanguage } from '../../contexts/LanguageContext';

interface PageTemplateProps {
  titleKey: string;
  description?: string;
  children?: React.ReactNode;
}

export default function PageTemplate({ titleKey, description, children }: PageTemplateProps) {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto px-6 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t(titleKey)}
          </h1>
          {description && (
            <p className="text-lg text-gray-600">
              {description}
            </p>
          )}
        </div>

        {/* Page Content */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          {children ? (
            children
          ) : (
            <div className="text-center py-12">
              <div className="mb-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                {t(titleKey)}
              </h3>
              <p className="text-gray-500 mb-6">
                This page is under development. Content will be added soon.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-blue-800 text-sm">
                  <strong>Navigation Test:</strong> This page demonstrates that the navigation system is working correctly.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Page Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">Features</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Responsive layout</li>
              <li>• RTL/LTR support</li>
              <li>• Language switching</li>
              <li>• Navigation breadcrumbs</li>
            </ul>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">Status</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span>Navigation: Active</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                <span>Content: In Development</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span>Layout: Complete</span>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">Actions</h4>
            <div className="space-y-2">
              <button className="w-full text-left text-sm text-blue-600 hover:text-blue-800 py-1">
                → View Documentation
              </button>
              <button className="w-full text-left text-sm text-blue-600 hover:text-blue-800 py-1">
                → Test Language Switch
              </button>
              <button className="w-full text-left text-sm text-blue-600 hover:text-blue-800 py-1">
                → Return to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
