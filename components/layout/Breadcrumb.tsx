'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

interface BreadcrumbItem {
  label: string;
  href: string;
  isActive?: boolean;
}

export default function Breadcrumb() {
  const pathname = usePathname();
  const { t, dir } = useLanguage();

  // Navigation mapping for breadcrumbs
  const navigationMap: Record<string, string> = {
    '/': 'navigation.dashboard',
    '/location-monitor': 'navigation.locationMonitor',
    '/focused-trips': 'navigation.focusedTrips',
    '/my-assigned-ports': 'navigation.myAssignedPorts',
    '/dashboard': 'navigation.dashboard',
    '/configuration': 'navigation.configuration',
    '/suspicious-trips': 'navigation.suspiciousTrips',
    '/reports': 'navigation.reports',
  };

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      {
        label: 'Home',
        href: '/',
        isActive: pathname === '/'
      }
    ];

    if (pathname !== '/') {
      const translationKey = navigationMap[pathname];
      if (translationKey) {
        breadcrumbs.push({
          label: t(translationKey),
          href: pathname,
          isActive: true
        });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null;
  }

  const ChevronIcon = dir === 'rtl' ? 
    () => <ChevronRight className="w-4 h-4 rotate-180" /> : 
    () => <ChevronRight className="w-4 h-4" />;

  return (
    <nav className="fixed top-[112px] left-0 right-0 z-40 bg-gradient-to-r from-[var(--color-brand-primary)] to-[var(--color-brand-secondary)] border-b border-[var(--color-brand-secondary)] px-6 py-3">
      <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-white">
        <Home className="w-4 h-4 text-white/80" />
        
        {breadcrumbs.map((item, index) => (
          <div key={item.href} className="flex items-center space-x-2 rtl:space-x-reverse">
            {index > 0 && (
              <ChevronIcon />
            )}
            
            {item.isActive ? (
              <span className="text-white font-medium">
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="text-white/90 hover:text-white hover:underline"
              >
                {item.label}
              </Link>
            )}
          </div>
        ))}
      </div>
    </nav>
  );
}
