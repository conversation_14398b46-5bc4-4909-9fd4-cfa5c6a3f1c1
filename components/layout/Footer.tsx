'use client';

import { useLanguage } from '../../contexts/LanguageContext';

export default function Footer() {
  const { language, t } = useLanguage();

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-[var(--color-brand-primary)] to-[var(--color-brand-secondary)] border-t border-[var(--color-brand-secondary)] py-3 z-10">
      <div className="container mx-auto px-6">
        <div className="flex justify-center items-center">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse text-xs">
              <span className="text-white/90">{t('footer.poweredBy')} </span>
              <span className="font-medium text-white">
                {language === 'ar' ? t('footer.technicalUnited') : t('footer.technicalUnitedArabic')}
              </span>
              <span className="mx-1 text-white/70">|</span>
              <span className="font-medium text-white">
                {language === 'ar' ? t('footer.technicalUnitedArabic') : t('footer.technicalUnited')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
