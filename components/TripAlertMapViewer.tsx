"use client";

import React, { useState, useMemo, useRef, useEffect } from "react";
import { loadGoogleMapsAPI } from "../utils/googleMaps";
import { useLanguage } from "../hooks/useLanguage";
import { TripAlert } from "../types/trip_alert";

// Google Maps type declarations
/* eslint-disable @typescript-eslint/no-explicit-any */
type GoogleMap = {
  setCenter(latLng: any): void;
  setZoom(zoom: number): void;
  getZoom(): number;
  getBounds(): any;
  fitBounds(bounds: any): void;
  addListener(event: string, handler: () => void): any;
};

type GoogleMarker = {
  setMap(map: GoogleMap | null): void;
  getPosition(): any;
  setPosition(latLng: any): void;
  addListener(eventName: string, handler: () => void): any;
};

type GoogleInfoWindow = {
  open(map?: GoogleMap, anchor?: GoogleMarker): void;
  close(): void;
  setContent(content: string): void;
};



type GoogleMaps = {
  Map: any;
  Marker: any;
  InfoWindow: any;
  LatLng: any;
  LatLngBounds: any;
  Size: any;
  Point: any;
};

declare global {
  interface Window {
    google: {
      maps: GoogleMaps;
    };
  }
}
/* eslint-enable @typescript-eslint/no-explicit-any */

// Alert type mapping to icon files
const ALERT_ICON_MAP: Record<string, string> = {
  tracker_tamper: "/map_assets/TrackerTamper.svg",
  tracker_dropped: "/map_assets/TrackerDropped.svg",
  lock_tamper: "/map_assets/LockTamper.svg",
  lock_open: "/map_assets/LockOpen.svg",
  lock_connection_lost: "/map_assets/LockConnectionLost.svg",
  tracker_battery_low: "/map_assets/TrackerBatteryLow.svg",
  lock_low_battery: "/map_assets/LockLowBattery.svg",
  gsm_signal_lost: "/map_assets/GSMSignalLost.svg",
  gps_signal_lost: "/map_assets/GPSSignalLost.svg",
  geofence_entry_breach: "/map_assets/GeofenceEntry.svg",
  geofence_exit_breach: "/map_assets/GeofenceExit.svg",
  tracker_connection_lost: "/map_assets/TrackerConnectionLost.svg",
  trip_distance_exceeded: "/map_assets/TripDistanceExceeded.svg",
  trip_time_exceeded: "/map_assets/TripTimeExceeded.svg",
  over_speeding: "/map_assets/OverSpeeding.svg",
  truck_stopped: "/map_assets/TruckStopped.svg",
  wrong_direction: "/map_assets/WrongDirection.svg",
  ping: "/map_assets/ping.svg",
  stop_car: "/map_assets/stop-car.png",
  trip_activity: "/map_assets/tripe_act.png",
  stopped_exceeded: "/map_assets/stopped_exceeded.png",
  map_marker: "/map_assets/map-marker.svg"
};

// Filter button configuration
interface FilterButton {
  id: string;
  label: string;
  icon: string;
  alertType?: string;
  active: boolean;
  visible: boolean;
}



interface TripAlertMapViewerProps {
  tripAlerts: TripAlert[];
  selectedTripId?: string;
  onAlertClick?: (alert: TripAlert) => void;
  className?: string;
}

const TripAlertMapViewer: React.FC<TripAlertMapViewerProps> = ({
  tripAlerts,
  selectedTripId,
  onAlertClick,
  className = ""
}) => {
  const language = useLanguage();
  
  console.log('TripAlertMapViewer rendered with:', { 
    alertsCount: tripAlerts.length, 
    selectedTripId, 
    className 
  });
  
  // Simple translation function
  const t = (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      "map.filter.title": { en: "Alert Filters", ar: "مرشحات التنبيهات" },
      "map.filter.selectAll": { en: "Select All", ar: "تحديد الكل" },
      "map.filter.deselectAll": { en: "Deselect All", ar: "إلغاء تحديد الكل" },
      "map.filter.hide": { en: "Hide", ar: "إخفاء" },
      "map.filter.show": { en: "Show Filters", ar: "إظهار المرشحات" },
      "map.filter.ping": { en: "Ping", ar: "بينغ" },
      "map.filter.stopCar": { en: "Stop Car", ar: "إيقاف السيارة" },
      "map.filter.tripActivity": { en: "Trip Activity", ar: "نشاط الرحلة" },
      "map.filter.stoppedExceeded": { en: "Stopped Exceeded", ar: "تجاوز التوقف" },
      "map.filter.truckStopped": { en: "Truck Stopped", ar: "الشاحنة متوقفة" },
      "map.filter.trackerTamper": { en: "Tracker Tamper", ar: "تلاعب بالمتتبع" },
      "map.filter.trackerDropped": { en: "Tracker Dropped", ar: "سقوط المتتبع" },
      "map.filter.lockTamper": { en: "Lock Tamper", ar: "تلاعب بالقفل" },
      "map.filter.lockOpen": { en: "Lock Open", ar: "القفل مفتوح" },
      "map.filter.lockConnectionLost": { en: "Lock Connection Lost", ar: "فقدان اتصال القفل" },
      "map.filter.trackerBatteryLow": { en: "Tracker Battery Low", ar: "بطارية المتتبع منخفضة" },
      "map.filter.lockLowBattery": { en: "Lock Low Battery", ar: "بطارية القفل منخفضة" },
      "map.filter.gsmSignalLost": { en: "GSM Signal Lost", ar: "فقدان إشارة GSM" },
      "map.filter.gpsSignalLost": { en: "GPS Signal Lost", ar: "فقدان إشارة GPS" },
      "map.filter.geofenceEntry": { en: "Geofence Entry", ar: "دخول السياج الجغرافي" },
      "map.filter.geofenceExit": { en: "Geofence Exit", ar: "خروج السياج الجغرافي" },
      "map.filter.trackerConnectionLost": { en: "Tracker Connection Lost", ar: "فقدان اتصال المتتبع" },
      "map.filter.tripDistanceExceeded": { en: "Trip Distance Exceeded", ar: "تجاوز مسافة الرحلة" },
      "map.filter.tripTimeExceeded": { en: "Trip Time Exceeded", ar: "تجاوز وقت الرحلة" },
      "map.filter.overSpeeding": { en: "Over Speeding", ar: "تجاوز السرعة" },
      "map.filter.wrongDirection": { en: "Wrong Direction", ar: "اتجاه خاطئ" },
      "map.filter.mapMarker": { en: "Map Marker", ar: "علامة الخريطة" },
      "map.alerts.showing": { en: "Showing", ar: "عرض" },
      "map.alerts.of": { en: "of", ar: "من" },
      "map.alerts.total": { en: "alerts", ar: "تنبيه" },
      "map.alerts.tripFilter": { en: "Trip", ar: "الرحلة" }
    };
    return translations[key]?.[language] || key;
  };
  
  // Initialize filter buttons
  const [filterButtons, setFilterButtons] = useState<FilterButton[]>([
    { id: "ping", label: t("map.filter.ping"), icon: ALERT_ICON_MAP.ping, active: false, visible: true },
    { id: "stop_car", label: t("map.filter.stopCar"), icon: ALERT_ICON_MAP.stop_car, active: false, visible: true },
    { id: "trip_activity", label: t("map.filter.tripActivity"), icon: ALERT_ICON_MAP.trip_activity, active: false, visible: true },
    { id: "stopped_exceeded", label: t("map.filter.stoppedExceeded"), icon: ALERT_ICON_MAP.stopped_exceeded, alertType: "stopped_exceeded", active: false, visible: true },
    { id: "truck_stopped", label: t("map.filter.truckStopped"), icon: ALERT_ICON_MAP.truck_stopped, alertType: "truck_stopped", active: false, visible: true },
    { id: "tracker_tamper", label: t("map.filter.trackerTamper"), icon: ALERT_ICON_MAP.tracker_tamper, alertType: "tracker_tamper", active: false, visible: true },
    { id: "tracker_dropped", label: t("map.filter.trackerDropped"), icon: ALERT_ICON_MAP.tracker_dropped, alertType: "tracker_dropped", active: false, visible: true },
    { id: "lock_tamper", label: t("map.filter.lockTamper"), icon: ALERT_ICON_MAP.lock_tamper, alertType: "lock_tamper", active: false, visible: true },
    { id: "lock_open", label: t("map.filter.lockOpen"), icon: ALERT_ICON_MAP.lock_open, alertType: "lock_open", active: false, visible: true },
    { id: "lock_connection_lost", label: t("map.filter.lockConnectionLost"), icon: ALERT_ICON_MAP.lock_connection_lost, alertType: "lock_connection_lost", active: false, visible: true },
    { id: "tracker_battery_low", label: t("map.filter.trackerBatteryLow"), icon: ALERT_ICON_MAP.tracker_battery_low, alertType: "tracker_battery_low", active: false, visible: true },
    { id: "lock_low_battery", label: t("map.filter.lockLowBattery"), icon: ALERT_ICON_MAP.lock_low_battery, alertType: "lock_low_battery", active: false, visible: true },
    { id: "gsm_signal_lost", label: t("map.filter.gsmSignalLost"), icon: ALERT_ICON_MAP.gsm_signal_lost, alertType: "gsm_signal_lost", active: false, visible: true },
    { id: "gps_signal_lost", label: t("map.filter.gpsSignalLost"), icon: ALERT_ICON_MAP.gps_signal_lost, alertType: "gps_signal_lost", active: false, visible: true },
    { id: "geofence_entry_breach", label: t("map.filter.geofenceEntry"), icon: ALERT_ICON_MAP.geofence_entry_breach, alertType: "geofence_entry_breach", active: false, visible: true },
    { id: "geofence_exit_breach", label: t("map.filter.geofenceExit"), icon: ALERT_ICON_MAP.geofence_exit_breach, alertType: "geofence_exit_breach", active: false, visible: true },
    { id: "tracker_connection_lost", label: t("map.filter.trackerConnectionLost"), icon: ALERT_ICON_MAP.tracker_connection_lost, alertType: "tracker_connection_lost", active: false, visible: true },
    { id: "trip_distance_exceeded", label: t("map.filter.tripDistanceExceeded"), icon: ALERT_ICON_MAP.trip_distance_exceeded, alertType: "trip_distance_exceeded", active: false, visible: true },
    { id: "trip_time_exceeded", label: t("map.filter.tripTimeExceeded"), icon: ALERT_ICON_MAP.trip_time_exceeded, alertType: "trip_time_exceeded", active: false, visible: true },
    { id: "over_speeding", label: t("map.filter.overSpeeding"), icon: ALERT_ICON_MAP.over_speeding, alertType: "over_speeding", active: false, visible: true },
    { id: "wrong_direction", label: t("map.filter.wrongDirection"), icon: ALERT_ICON_MAP.wrong_direction, alertType: "wrong_direction", active: false, visible: true },
    { id: "map_marker", label: t("map.filter.mapMarker"), icon: ALERT_ICON_MAP.map_marker, active: false, visible: true }
  ]);

  const [filterBarVisible, setFilterBarVisible] = useState(true);

  // Toggle filter button active state
  const toggleFilter = (buttonId: string) => {
    setFilterButtons(prev => 
      prev.map(button => 
        button.id === buttonId 
          ? { ...button, active: !button.active }
          : button
      )
    );
  };



  // Filter alerts based on active filters and selected trip
  const filteredAlerts = useMemo(() => {
    let alerts = tripAlerts;
    
    // Filter by selected trip if specified
    if (selectedTripId) {
      alerts = alerts.filter(alert => alert.tripId === selectedTripId);
    }
    
    // Filter by active alert type filters
    const activeAlertTypes = filterButtons
      .filter(button => button.active && button.alertType)
      .map(button => button.alertType);
    
    // If no alert type filters are active, show all alerts
    // If some alert type filters are active, only show those types
    if (activeAlertTypes.length === 0) {
      return alerts; // Show all alerts when no filters are active
    }
    
    alerts = alerts.filter(alert => activeAlertTypes.includes(alert.alertType));
    
    return alerts;
  }, [tripAlerts, selectedTripId, filterButtons]);

  // Map state
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<GoogleMap | null>(null);
  const [markers, setMarkers] = useState<GoogleMarker[]>([]);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [activeInfoWindow, setActiveInfoWindow] = useState<GoogleInfoWindow | null>(null);

  // Calculate map center based on alerts
  const mapCenter = useMemo(() => {
    if (filteredAlerts.length === 0) {
      // Default to Saudi Arabia center
      return { lat: 24.7136, lng: 46.6753 };
    }
    
    const avgLat = filteredAlerts.reduce((sum, alert) => sum + alert.location.latitude, 0) / filteredAlerts.length;
    const avgLng = filteredAlerts.reduce((sum, alert) => sum + alert.location.longitude, 0) / filteredAlerts.length;
    
    return { lat: avgLat, lng: avgLng };
  }, [filteredAlerts]);

  // Load Google Maps API
  useEffect(() => {
    console.log('🗺️ TripAlertMapViewer: Loading Google Maps API...');
    console.log('🔑 API Key available:', !!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY);
    loadGoogleMapsAPI()
      .then(() => {
        console.log('✅ Google Maps API loaded successfully');
        console.log('🌐 window.google available:', !!window.google);
        console.log('🗺️ window.google.maps available:', !!(window.google && window.google.maps));
        setIsMapLoaded(true);
      })
      .catch((error) => {
        console.error('❌ Failed to load Google Maps:', error);
      });
  }, []);

  // Initialize map
  useEffect(() => {
    console.log('🎯 Map initialization effect:', { isMapLoaded, hasMapRef: !!mapRef.current, hasMap: !!map });
    if (!isMapLoaded || !mapRef.current || map) {
      console.log('⏸️ Map initialization skipped:', { isMapLoaded, hasMapRef: !!mapRef.current, hasMap: !!map });
      return;
    }

    console.log('🏗️ Creating map instance with center:', mapCenter);
    console.log('📏 Map container dimensions:', {
      width: mapRef.current.offsetWidth,
      height: mapRef.current.offsetHeight,
      clientWidth: mapRef.current.clientWidth,
      clientHeight: mapRef.current.clientHeight
    });
    
    try {
      const mapInstance = new window.google.maps.Map(mapRef.current, {
        center: mapCenter,
        zoom: 8,
        mapTypeId: 'roadmap',
      });

      console.log('✅ Map instance created successfully:', mapInstance);
      setMap(mapInstance);
    } catch (error) {
      console.error('❌ Error creating map instance:', error);
    }
  }, [isMapLoaded, map, mapCenter]);

  // Create marker icon
  const createMarkerIcon = (alertType: string) => {
    const iconUrl = ALERT_ICON_MAP[alertType] || ALERT_ICON_MAP.map_marker;
    return {
      url: iconUrl,
      scaledSize: new window.google.maps.Size(32, 32),
      anchor: new window.google.maps.Point(16, 16),
    };
  };

  // Update markers when alerts change
  useEffect(() => {
    if (!map || !isMapLoaded) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    // Create new markers
    const newMarkers = filteredAlerts.map((alert) => {
      const marker = new window.google.maps.Marker({
        position: { lat: alert.location.latitude, lng: alert.location.longitude },
        map: map,
        title: alert.description,
        icon: createMarkerIcon(alert.alertType),
      }) as GoogleMarker;

      // Add info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 12px; min-width: 220px; font-family: system-ui, -apple-system, sans-serif;">
            <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px; color: #1f2937; line-height: 1.2;">
              ${alert.description}
            </h3>
            <p style="font-size: 13px; color: #6b7280; margin-bottom: 6px; line-height: 1.4;">
              📍 ${alert.location.address}
            </p>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
              <span style="font-size: 12px; color: #2563eb; font-weight: 500; background: #eff6ff; padding: 2px 8px; border-radius: 12px;">
                ${alert.alertType.replace('_', ' ')}
              </span>
            </div>
            <p style="font-size: 12px; color: #059669; margin-top: 8px; font-weight: 500;">
              ${language === "ar" ? "الحالة:" : "Status:"}
              <span style="color: ${alert.severity === 'high' ? '#dc2626' : '#059669'};">
                ${alert.severity}
              </span>
            </p>
            <p style="font-size: 11px; color: #6b7280; margin-top: 4px;">
              ${new Date(alert.timestamp).toLocaleString()}
            </p>
          </div>
        `,
      });

      marker.addListener('click', () => {
        // Close any existing info window
        if (activeInfoWindow) {
          activeInfoWindow.close();
        }

        // Open new info window
        infoWindow.open(map, marker);
        setActiveInfoWindow(infoWindow);

        if (onAlertClick) {
          onAlertClick(alert);
        }
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Add click listener to map to close info windows
    map.addListener('click', () => {
      if (activeInfoWindow) {
        activeInfoWindow.close();
        setActiveInfoWindow(null);
      }
    });

    // Cleanup function
    return () => {
      newMarkers.forEach(marker => marker.setMap(null));
    };
  }, [map, filteredAlerts, isMapLoaded, language]);

  // Update map center when alerts change
  useEffect(() => {
    if (map && filteredAlerts.length > 0) {
      map.setCenter(mapCenter);
    }
  }, [map, mapCenter, filteredAlerts.length]);

  return (
    <div className={`trip-alert-map-viewer ${className} flex flex-col`}>
      {/* Filter Bar */}
      {filterBarVisible && (
        <div className="filter-bar bg-white border-b border-gray-200 p-2 z-10 flex-shrink-0">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-1 flex-wrap">
              {filterButtons
                .filter(button => button.visible) // Show all visible filter buttons
                .map(button => (
                  <button
                    key={button.id}
                    onClick={() => toggleFilter(button.id)}
                    className={`p-1.5 rounded border transition-all duration-200 ${
                      button.active
                        ? "bg-blue-100 border-blue-300"
                        : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                    }`}
                    title={button.label}
                  >
                    <img
                      src={button.icon}
                      alt={button.label}
                      className="w-4 h-4"
                    />
                  </button>
                ))
              }
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={() => setFilterButtons(prev => prev.map(b => ({ ...b, active: true })))}
                className="p-1.5 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                title={t("map.filter.selectAll")}
              >
                ✓
              </button>
              <button
                onClick={() => setFilterButtons(prev => prev.map(b => ({ ...b, active: false })))}
                className="p-1.5 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                title={t("map.filter.deselectAll")}
              >
                ✗
              </button>
              <button
                onClick={() => setFilterBarVisible(false)}
                className="p-1.5 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                title={t("map.filter.hide")}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Map Section */}
      <div className="map-section flex-1 relative min-h-0">
        {/* Show Filter Bar Button - Positioned over map */}
        {!filterBarVisible && (
          <div className="absolute top-4 left-4 z-20">
            <button
              onClick={() => setFilterBarVisible(true)}
              className="px-4 py-2 bg-white shadow-lg border border-gray-200 text-blue-700 rounded hover:bg-blue-50 transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
              {t("map.filter.show")} ({filteredAlerts.length})
            </button>
          </div>
        )}
        

        
        <div className="absolute inset-0 w-full h-full">
          {!isMapLoaded ? (
            <div className="flex items-center justify-center w-full h-full bg-gray-100">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">{language === "ar" ? "جاري تحميل الخريطة..." : "Loading map..."}</p>
              </div>
            </div>
          ) : (
            <div 
              ref={mapRef} 
              className="w-full h-full" 
              style={{ minHeight: '400px' }}
            />
          )}
        </div>
      </div>
      
      {/* Compact Footer */}
      <div className="bg-gray-50 border-t border-gray-200 px-4 py-2 flex-shrink-0">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            Showing {filteredAlerts.length} of {tripAlerts.length} alerts
          </span>
          {selectedTripId && (
            <span className="text-blue-600">
              Trip: {selectedTripId}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default TripAlertMapViewer;