'use client';

import { useState, useEffect } from 'react';

interface NoSSRProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * NoSSR Component
 * 
 * Prevents server-side rendering of child components to avoid hydration mismatches.
 * Useful for components that rely on browser-specific APIs or have dynamic content
 * that differs between server and client.
 * 
 * @param children - Components to render only on client side
 * @param fallback - Optional fallback to show during server-side rendering
 */
export default function NoSSR({ children, fallback = null }: NoSSRProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
