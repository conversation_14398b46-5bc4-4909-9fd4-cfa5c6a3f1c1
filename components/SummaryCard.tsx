/**
 * Enhanced SummaryCard Component
 *
 * A modern, responsive card component for displaying dashboard statistics
 * with support for predefined icons, custom icons, internationalization,
 * and multiple color themes.
 *
 * Features:
 * - Predefined icon system (clock, bell-orange, bell-gray, check, prohibition)
 * - Custom React component icons
 * - Internationalization support via translation keys
 * - Modern design with gradients and hover effects
 * - Responsive design
 * - Loading states
 * - Click handlers
 */

import React from "react";
import {
  Bell,
  BellOff,
  Check,
  Ban,
  LucideIcon,
  TrendingUp,
  TrendingDown,
  Minus,
} from "lucide-react";
import { useLanguage } from "../contexts/LanguageContext";

export interface SummaryCard {
  id: string;
  titleKey: string; // Translation key instead of hardcoded text
  value: number | string;
  icon:
    | "clock"
    | "bell-orange"
    | "bell-gray"
    | "bell-off"
    | "check"
    | "prohibition"
    | "custom";
  customIcon?: React.ReactNode;
  color: "blue" | "orange" | "gray" | "green" | "red";
  onClick?: () => void;
  loading?: boolean;
  trend?: {
    value: number;
    direction: "up" | "down" | "stable";
  };
}

export interface SummaryCardProps extends SummaryCard {
  size?: "sm" | "md" | "lg";
  className?: string;
  showTrends?: boolean;
}

// Icon mapping system
const iconMap: Record<string, LucideIcon> = {
  "bell-orange": Bell,
  "bell-gray": Bell,
  "bell-off": BellOff,
  check: Check,
  prohibition: Ban,
};

// Premium color system with enhanced gradients and modern effects
const colorStyles = {
  blue: {
    card: "bg-gradient-to-br from-blue-50 via-indigo-50/80 to-blue-100 border-blue-200/60 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-500/10 backdrop-blur-sm",
    text: "text-blue-900 font-medium",
    icon: "text-blue-600 drop-shadow-sm",
    iconBg:
      "bg-gradient-to-br from-blue-500/15 to-blue-600/10 hover:from-blue-500/25 hover:to-blue-600/15 ring-1 ring-blue-500/20",
    value: "text-blue-900 font-bold tracking-tight",
  },
  orange: {
    card: "bg-gradient-to-br from-orange-50 via-amber-50/80 to-orange-100 border-orange-200/60 hover:border-orange-300 hover:shadow-xl hover:shadow-orange-500/10 backdrop-blur-sm",
    text: "text-orange-900 font-medium",
    icon: "text-orange-600 drop-shadow-sm",
    iconBg:
      "bg-gradient-to-br from-orange-500/15 to-orange-600/10 hover:from-orange-500/25 hover:to-orange-600/15 ring-1 ring-orange-500/20",
    value: "text-orange-900 font-bold tracking-tight",
  },
  gray: {
    card: "bg-gradient-to-br from-gray-50 via-slate-50/80 to-gray-100 border-gray-200/60 hover:border-gray-300 hover:shadow-xl hover:shadow-gray-500/10 backdrop-blur-sm",
    text: "text-gray-900 font-medium",
    icon: "text-gray-600 drop-shadow-sm",
    iconBg:
      "bg-gradient-to-br from-gray-500/15 to-gray-600/10 hover:from-gray-500/25 hover:to-gray-600/15 ring-1 ring-gray-500/20",
    value: "text-gray-900 font-bold tracking-tight",
  },
  green: {
    card: "bg-gradient-to-br from-green-50 via-emerald-50/80 to-green-100 border-green-200/60 hover:border-green-300 hover:shadow-xl hover:shadow-green-500/10 backdrop-blur-sm",
    text: "text-green-900 font-medium",
    icon: "text-green-600 drop-shadow-sm",
    iconBg:
      "bg-gradient-to-br from-green-500/15 to-green-600/10 hover:from-green-500/25 hover:to-green-600/15 ring-1 ring-green-500/20",
    value: "text-green-900 font-bold tracking-tight",
  },
  red: {
    card: "bg-gradient-to-br from-red-50 via-rose-50/80 to-red-100 border-red-200/60 hover:border-red-300 hover:shadow-xl hover:shadow-red-500/10 backdrop-blur-sm",
    text: "text-red-900 font-medium",
    icon: "text-red-600 drop-shadow-sm",
    iconBg:
      "bg-gradient-to-br from-red-500/15 to-red-600/10 hover:from-red-500/25 hover:to-red-600/15 ring-1 ring-red-500/20",
    value: "text-red-900 font-bold tracking-tight",
  },
};

// Compact size variants matching reference design
const sizeStyles = {
  sm: {
    card: "p-4 h-[100px] w-full max-w-[280px]",
    icon: "w-5 h-5",
    iconContainer: "p-2 w-10 h-10",
    value: "text-xl",
    label: "text-xs font-medium",
  },
  md: {
    card: "p-5 h-[110px] w-full max-w-[320px]",
    icon: "w-6 h-6",
    iconContainer: "p-2.5 w-11 h-11",
    value: "text-2xl",
    label: "text-sm font-medium",
  },
  lg: {
    card: "p-6 h-[120px] w-full max-w-[360px]",
    icon: "w-7 h-7",
    iconContainer: "p-3 w-12 h-12",
    value: "text-3xl",
    label: "text-base font-medium",
  },
};

export default function SummaryCard({
  titleKey,
  value,
  icon,
  customIcon,
  color,
  onClick,
  loading = false,
  trend,
  size = "sm",
  className = "",
  showTrends = false,
}: SummaryCardProps) {
  const { t, dir } = useLanguage();

  const colorStyle = colorStyles[color];
  const sizeStyle = sizeStyles[size];

  // Get the appropriate icon
  const IconComponent = iconMap[icon];
  const iconElement =
    customIcon ||
    (IconComponent && (
      <IconComponent className={`${sizeStyle.icon} ${colorStyle.icon}`} />
    ));

  // Format value for display
  const displayValue =
    typeof value === "number" ? value.toLocaleString() : value;

  return (
    <div
      className={`
        ${sizeStyle.card}
        ${colorStyle.card}
        rounded-xl border shadow-sm
        transition-all duration-200 ease-out
        hover:shadow-md hover:scale-[1.02]
        ${
          onClick
            ? "cursor-pointer hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/30"
            : "hover:shadow-md"
        }
        ${loading ? "animate-pulse opacity-60" : ""}
        relative overflow-hidden group
        flex flex-row items-center justify-between
        ${className}
      `}
      onClick={onClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={
        onClick
          ? (e: React.KeyboardEvent) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                onClick();
              }
            }
          : undefined
      }
    >
      {/* Icon section */}
      <div
        className={`
          ${sizeStyle.iconContainer}
          ${colorStyle.iconBg}
          rounded-lg
          transition-all duration-200 ease-out
          flex items-center justify-center
          group-hover:scale-105
          shadow-sm
          relative z-10 shrink-0
        `}
      >
        {loading ? (
          <div
            className={`${sizeStyle.icon} bg-gray-300 rounded animate-pulse`}
          />
        ) : (
          iconElement
        )}
      </div>

      {/* Content section */}
      <div
        className={`flex-1 flex flex-col justify-center ${
          dir === "rtl" ? "text-right pr-4" : "text-left pl-4"
        }`}
      >
        {/* Value */}
        {loading ? (
          <div className="h-6 w-16 bg-gray-300 rounded-md animate-pulse mb-1" />
        ) : (
          <p
            className={`${sizeStyle.value} ${colorStyle.value} leading-tight font-bold tracking-tight relative z-10 mb-1`}
            title={`Value: ${displayValue}`}
          >
            {displayValue}
          </p>
        )}

        {/* Label */}
        {loading ? (
          <div className="h-4 w-24 bg-gray-300 rounded-md animate-pulse" />
        ) : (
          <p
            className={`${sizeStyle.label} ${colorStyle.text} leading-snug relative z-10 line-clamp-1 opacity-80`}
            title={t(titleKey)}
          >
            {t(titleKey)}
          </p>
        )}
      </div>

      {/* Trend indicator (if enabled and trend data exists) */}
      {showTrends && trend && !loading && (
        <div className="ml-auto flex items-center">
          <div
            className={`
            inline-flex items-center justify-center px-2 py-1 rounded-md text-xs font-medium
            trend-indicator cursor-default
            shadow-sm transition-all duration-200
            border relative z-10
            ${
              trend.direction === "up"
                ? "trend-up bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
                : trend.direction === "down"
                ? "trend-down bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
                : "trend-stable bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100"
            }
          `}
            title={`Trend: ${
              trend.direction === "stable"
                ? "No change"
                : `${Math.abs(trend.value)}% ${trend.direction}`
            }`}
          >
            <span
              className={`
              mr-1 flex items-center
              ${
                trend.direction === "up"
                  ? "text-green-600"
                  : trend.direction === "down"
                  ? "text-red-600"
                  : "text-gray-600"
              }
            `}
            >
              {trend.direction === "up" ? (
                <TrendingUp className="w-3 h-3" />
              ) : trend.direction === "down" ? (
                <TrendingDown className="w-3 h-3" />
              ) : (
                <Minus className="w-3 h-3" />
              )}
            </span>
            <span className="font-medium text-xs">
              {trend.direction === "stable"
                ? "0%"
                : `${Math.abs(trend.value)}%`}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
