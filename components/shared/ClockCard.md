# ClockCard Component

## Overview

The `ClockCard` is a highly configurable React component that displays a real-time clock with customizable timezone, date format, time format, and display style options. It provides a clean, modern interface with optional settings panel for user customization.

## Features

- **Real-time Clock Display**: Shows current time with automatic updates
- **Timezone Support**: Configurable timezone selection with major world timezones
- **Multiple Display Styles**: Compact, detailed, and default view modes
- **Customizable Formats**: Flexible date and time format options
- **UI Control**: Granular control over which settings are available to users
- **Responsive Design**: Adapts to different screen sizes
- **Internationalization**: Supports multiple languages through context

## Props Interface

```typescript
interface ClockCardProps {
  // Display Configuration
  timezone?: string;                    // Default: 'America/New_York'
  dateFormat?: string;                  // Default: 'MMMM d, yyyy'
  timeFormat?: string;                  // Default: 'h:mm:ss a'
  displayStyle?: 'default' | 'compact' | 'detailed'; // Default: 'default'
  
  // UI Control Props
  enableTimezoneControl?: boolean;      // Default: true
  enableDateFormatControl?: boolean;    // Default: true
  enableTimeFormatControl?: boolean;    // Default: true
  enableDisplayStyleControl?: boolean;  // Default: true
  
  // Layout
  className?: string;                   // Additional CSS classes
}
```

## Usage Examples

### Basic Usage

```tsx
import { ClockCard } from '@/components/shared/ClockCard';

function App() {
  return (
    <div>
      <ClockCard />
    </div>
  );
}
```

### Custom Timezone and Format

```tsx
<ClockCard 
  timezone="Europe/London"
  dateFormat="dd/MM/yyyy"
  timeFormat="HH:mm:ss"
  displayStyle="detailed"
/>
```

### Limited User Controls

```tsx
// Only allow time and date format changes
<ClockCard 
  enableTimezoneControl={false}
  enableDisplayStyleControl={false}
  enableTimeFormatControl={true}
  enableDateFormatControl={true}
/>
```

### Display-Only Clock

```tsx
// No user controls - display only
<ClockCard 
  enableTimezoneControl={false}
  enableDateFormatControl={false}
  enableTimeFormatControl={false}
  enableDisplayStyleControl={false}
  timezone="UTC"
  displayStyle="compact"
/>
```

## Display Styles

### Default Style
- Standard clock display with time and date
- Balanced information density
- Suitable for most use cases

### Compact Style
- Minimal space usage
- Essential information only
- Ideal for dashboards or sidebars

### Detailed Style
- Maximum information display
- Includes additional time details
- Best for dedicated time displays

## Supported Timezones

The component supports major world timezones including:

- **Americas**: New York, Los Angeles, Chicago, Toronto, São Paulo
- **Europe**: London, Paris, Berlin, Rome, Madrid
- **Asia**: Tokyo, Shanghai, Mumbai, Dubai, Singapore
- **Pacific**: Sydney, Auckland
- **UTC**: Coordinated Universal Time

## Date Format Options

- `MMMM d, yyyy` - January 15, 2024
- `dd/MM/yyyy` - 15/01/2024
- `MM/dd/yyyy` - 01/15/2024
- `yyyy-MM-dd` - 2024-01-15
- `EEEE, MMMM d` - Monday, January 15

## Time Format Options

- `h:mm:ss a` - 2:30:45 PM (12-hour with seconds)
- `h:mm a` - 2:30 PM (12-hour without seconds)
- `HH:mm:ss` - 14:30:45 (24-hour with seconds)
- `HH:mm` - 14:30 (24-hour without seconds)

## Implementation Details

### Dependencies

- React hooks for state management and effects
- Tailwind CSS for styling
- Lucide React icons for UI elements
- Custom HijriDate utility for Islamic calendar conversion
- Native JavaScript Date API for time handling and timezone conversion

### State Management

The component uses React's `useState` and `useEffect` hooks to:
- Manage current time updates
- Handle user preference changes
- Control settings panel visibility
- Maintain timezone conversions
- Prevent hydration mismatches with `isMounted` state

### React 19 Compatibility

While React 19 introduces the new `use` hook for data fetching and context consumption <mcreference link="https://medium.com/@ademyalcin27/the-new-use-hook-in-react-19-a-game-changer-for-simpler-data-fetching-and-context-management-cc45cc5ebd28" index="2">2</mcreference>, it cannot replace `useEffect` for timer-based operations like `setInterval`. The `use` hook is designed for asynchronous operations that resolve once (promises), while our clock component requires a persistent timer that runs continuously <mcreference link="https://overreacted.io/making-setinterval-declarative-with-react-hooks/" index="1">1</mcreference>. Therefore, `useEffect` remains the correct choice for managing the clock's timer functionality.

### Performance Considerations

- Time updates every second using `setInterval` <mcreference link="https://upmostly.com/tutorials/setinterval-in-react-components-using-hooks" index="3">3</mcreference>
- Cleanup of intervals on component unmount to prevent memory leaks
- Efficient re-rendering with proper dependency arrays
- Hydration-safe rendering with `isMounted` state check

## Accessibility

- Semantic HTML structure
- Proper ARIA labels for interactive elements
- Keyboard navigation support
- Screen reader compatible
- High contrast color schemes

## Browser Compatibility

- Modern browsers with ES6+ support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Styling

The component uses Tailwind CSS classes and can be customized through:

- `className` prop for additional styles
- CSS custom properties for theme customization
- Tailwind configuration modifications
- Component-level style overrides

## Error Handling

- Graceful fallback for invalid timezones
- Default format application for invalid date/time formats
- Console warnings for development debugging
- Robust error boundaries integration

## Testing

### Unit Tests

```typescript
// Example test structure
describe('ClockCard', () => {
  it('renders with default props', () => {
    // Test implementation
  });
  
  it('updates time every second', () => {
    // Test implementation
  });
  
  it('respects UI control props', () => {
    // Test implementation
  });
});
```

### Integration Tests

- Timezone conversion accuracy
- Format application correctness
- User interaction workflows
- Settings persistence

## Contributing

### Development Setup

1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Run tests: `npm test`
4. Build for production: `npm run build`

### Code Standards

- TypeScript strict mode
- ESLint configuration compliance
- Prettier code formatting
- Comprehensive prop documentation

## Changelog

### v2.0.0
- Added UI control props for granular feature management
- Enhanced timezone support
- Improved accessibility features
- Performance optimizations

### v1.0.0
- Initial release
- Basic clock functionality
- Timezone and format support
- Settings panel implementation

## License

This component is part of the TTS Template project and follows the project's licensing terms.