"use client";

import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { useState, useMemo, Fragment } from "react";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { AlertCircleIcon } from "lucide-react";

interface Column<T> {
  key: string;
  title: string;
  render?: (row: T) => React.ReactNode;
  width?: string;
  align?: "left" | "center" | "right";
  sticky?: boolean;
}

interface Action<T> {
  icon: React.ReactNode;
  abelKey: string;
  onClick: (row: T) => void;
  variant?: "primary" | "secondary" | "danger";
  visible?: (row: T) => boolean;
  disabled?: (row: T) => boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  keyField: keyof T;
  actions?: Action<T>[];

  pageSize?: number;
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  selectable?: boolean;

  expandedRowRender?: (row: T) => React.ReactNode;
  expandable?: boolean;
  expandedRowKeys?: string[];
  onExpandedRowsChange?: (expandedKeys: string[]) => void;
  expandRowByClick?: boolean;
  expandIconColumnIndex?: number;
  expandIcon?: (expanded: boolean) => React.ReactNode;
  defaultExpandAllRows?: boolean;
  expandedRowClassName?: string;

  exportable?: boolean;
  exportFormats?: ("xlsx" | "csv" | "pdf")[];
  exportFileNameKey?: string;
  onExport?: (format: string, data: T[]) => void;

  rowStatusField?: string;
  statusConfig?: {
    [key: string]: {
      color: string;
      icon?: React.ReactNode;
      highlight?: boolean;
    };
  };

  loading?: boolean;
  error?: string;
  emptyMessageKey?: string;

  className?: string;
  size?: "sm" | "md" | "lg";
  striped?: boolean;
  bordered?: boolean;
  stickyHeader?: boolean;
}

export function DataTable<T extends object>({
  data,
  columns,
  keyField,
  actions,
  expandable = false,
  expandedRowRender,
  rowStatusField,
  statusConfig,
  stickyHeader = false,
  expandRowByClick = false,
  exportable,
  exportFormats,
  exportFileNameKey,
  pageSize = 10,
  searchable,
  sortable,
  filterable,
  selectable,
}: DataTableProps<T>) {
  const [expandedKey, setExpandedKey] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [columnFilters, setColumnFilters] = useState<{ [key: string]: string }>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const hasActions = actions && actions.length > 0;

  const toggleRow = (key: string) => {
    setExpandedKey((prev) => (prev === key ? null : key));
  };

  const isRowSelected = (key: string) => selectedRowKeys.includes(key);

  const toggleSelectAll = () => {
    const currentKeys = paginatedData.map((row) => String(row[keyField]));
    const allSelected = currentKeys.every((key) => selectedRowKeys.includes(key));

    if (allSelected) {
      setSelectedRowKeys((prev) => prev.filter((k) => !currentKeys.includes(k)));
    } else {
      setSelectedRowKeys((prev) => Array.from(new Set([...prev, ...currentKeys])));
    }
  };

  const toggleRowSelection = (key: string) => {
    setSelectedRowKeys((prev) =>
      prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]
    );
  };

  const exportFile = (format: "xlsx" | "csv" | "pdf") => {
    if (!exportFileNameKey) return;

    if (format === "pdf") {
      const doc = new jsPDF();

      if (data.length === 0) {
        doc.text("No data available", 10, 10);
      } else {
        const columnsKeys = Object.keys(data[0]);
        const rows = data.map((row) =>
          columnsKeys.map((key) => String((row as Record<string, unknown>)[key] || ''))
        );

        autoTable(doc, {
          head: [columnsKeys],
          body: rows,
          startY: 20,
        });
      }

      doc.save(`${exportFileNameKey}.pdf`);
      return;
    }

    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    const wbout =
      format === "csv"
        ? XLSX.write(workbook, { bookType: "csv", type: "array" })
        : XLSX.write(workbook, { bookType: "xlsx", type: "array" });

    const blob = new Blob([wbout], {
      type:
        format === "csv"
          ? "text/csv;charset=utf-8;"
          : "application/octet-stream",
    });

    saveAs(blob, `${exportFileNameKey}.${format}`);
  };

  const filteredData = useMemo(() => {
    let filtered = [...data];

    if (searchable && searchQuery.trim()) {
      const q = searchQuery.toLowerCase();
      filtered = filtered.filter((row) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(q)
        )
      );
    }

    if (filterable) {
      Object.entries(columnFilters).forEach(([key, value]) => {
        if (value.trim()) {
          filtered = filtered.filter((row) =>
            String((row as Record<string, unknown>)[key])
              .toLowerCase()
              .includes(value.toLowerCase())
          );
        }
      });
    }

    return filtered;
  }, [data, searchQuery, columnFilters]);

  const sortedData = useMemo(() => {
    if (!sortable || !sortConfig) return filteredData;

    return [...filteredData].sort((a, b) => {
      const aVal = a[sortConfig.key as keyof T];
      const bVal = b[sortConfig.key as keyof T];
      if (aVal === bVal) return 0;
      if (aVal == null) return 1;
      if (bVal == null) return -1;

      return sortConfig.direction === "asc"
        ? String(aVal).localeCompare(String(bVal))
        : String(bVal).localeCompare(String(aVal));
    });
  }, [filteredData, sortConfig]);

  const totalPages = Math.ceil(sortedData.length / pageSize);
  const paginatedData = sortedData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <div className=" mt-5">
      {exportable && exportFormats?.length && exportFileNameKey && (
        <div className="flex gap-2 py-4">
          {exportFormats.map((format) => (
            <button
              key={format}
              className="border px-4 py-1 rounded bg-white text-sm hover:bg-gray-100"
              onClick={() => exportFile(format)}
            >
              Export as {format.toUpperCase()}
            </button>
          ))}
        </div>
      )}

      {searchable && (
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search..."
            className="border rounded px-4 py-2 w-full text-sm"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
      )}

      <div className="w-full overflow-y-auto shadow-sm bg-white">
        <Table className="w-full border-collapse text-sm">
          <TableHeader>
            <TableRow className="bg-white text-muted-foreground">
              {selectable && (
                <TableHead
                  className={cn(
                    "px-4 py-2",
                    stickyHeader && "sticky top-0 bg-white z-10 shadow-sm"
                  )}
                >
                  <input
                    type="checkbox"
                    checked={
                      paginatedData.length > 0 &&
                      paginatedData.every((row) =>
                        selectedRowKeys.includes(String(row[keyField]))
                      )
                    }
                    onChange={toggleSelectAll}
                  />
                </TableHead>
              )}

              {expandable && (
                <TableHead
                  className={cn(
                    "px-4 py-2 text-black",
                    stickyHeader && "sticky top-0 bg-white z-10 shadow-sm"
                  )}
                >
                  Details
                </TableHead>
              )}

              {columns.map((col) => (
                <TableHead
                  key={col.key}
                  onClick={() => {
                    if (!sortable) return;
                    setSortConfig((prev) =>
                      prev?.key === col.key
                        ? {
                            key: col.key,
                            direction: prev.direction === "asc" ? "desc" : "asc",
                          }
                        : { key: col.key, direction: "asc" }
                    );
                  }}
                  style={{ width: col.width }}
                  className={cn(
                    "px-4 py-2 text-blue-500 cursor-pointer",
                    col.align && `text-${col.align}`,
                    stickyHeader && "sticky top-0 bg-white z-10 shadow-sm"
                  )}
                >
                  {col.title}
                  {sortable && sortConfig?.key === col.key && (
                    <span className="ml-1 text-2xl">
                      {sortConfig.direction === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </TableHead>
              ))}

              {hasActions && (
                <TableHead
                  className={cn(
                    "text-center",
                    stickyHeader && "sticky top-0 bg-white z-10"
                  )}
                >
                  Actions
                </TableHead>
              )}
            </TableRow>

            {filterable && (
              <TableRow>
                {selectable && <TableCell />}
                {expandable && <TableCell />}
                {columns.map((col) => (
                  <TableCell key={col.key}>
                    <input
                      type="text"
                      value={columnFilters[col.key] || ""}
                      onChange={(e) =>
                        setColumnFilters((prev) => ({
                          ...prev,
                          [col.key]: e.target.value,
                        }))
                      }
                      placeholder="Filter"
                      className="w-full px-2 py-1 border rounded text-sm"
                    />
                  </TableCell>
                ))}
                {hasActions && <TableCell />}
              </TableRow>
            )}
          </TableHeader>

          <TableBody>
            {paginatedData.map((row) => {
              const rowKey = String(row[keyField]);
              const isExpanded = expandedKey === rowKey;

              return (
                <Fragment key={rowKey}>
                  <TableRow
                    className="cursor-pointer"
                    onClick={
                      expandRowByClick ? () => toggleRow(rowKey) : undefined
                    }
                  >
                    {selectable && (
                      <TableCell className="px-4 py-2">
                        <input
                          type="checkbox"
                          checked={isRowSelected(rowKey)}
                          onChange={(e) => {
                            e.stopPropagation();
                            toggleRowSelection(rowKey);
                          }}
                        />
                      </TableCell>
                    )}

                    {expandable && (
                      <TableCell
                        className="px-4 py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleRow(rowKey);
                        }}
                      >
                        <div className="flex">
                          <AlertCircleIcon
                            fill="yellowGreen"
                            size={22}
                            className="text-white"
                          />
                        </div>
                      </TableCell>
                    )}

                    {columns.map((col) => {
                      const value = (row as Record<string, unknown>)[col.key as string];

                      if (
                        rowStatusField &&
                        col.key === rowStatusField &&
                        value &&
                        typeof value === 'string' &&
                        statusConfig?.[value]
                      ) {
                        const status = statusConfig[value];
                        return (
                          <TableCell
                            key={col.key}
                            className={cn(
                              "px-4 py-2 text-center",
                              status.highlight && `bg-${status.color}-100`
                            )}
                          >
                            <div className="flex justify-center items-center gap-1 text-sm">
                              {status.icon}
                            </div>
                          </TableCell>
                        );
                      }

                      return (
                        <TableCell
                          key={col.key}
                          className={cn("px-4 py-2", col.align && `text-${col.align}`)}
                        >
                          {col.render ? col.render(row) : String(value || '')}
                        </TableCell>
                      );
                    })}

                    {hasActions && (
                      <TableCell className="flex justify-center gap-2">
                        {actions
                          .filter((a) => a.visible?.(row) ?? true)
                          .map((a, idx) => (
                            <button
                              key={idx}
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                a.onClick(row);
                              }}
                            >
                              {a.icon}
                            </button>
                          ))}
                      </TableCell>
                    )}
                  </TableRow>

                  {isExpanded && expandedRowRender && (
                    <TableRow>
                      <TableCell className="bg-gray-100"
                        colSpan={
                          columns.length +
                          (hasActions ? 1 : 0) +
                          (expandable ? 1 : 0) +
                          (selectable ? 1 : 0)
                        }
                      >
                        <div className="">
                          {expandedRowRender(row)}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </Fragment>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {pageSize && totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-4">
          <button
            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded text-sm"
          >
            Prev
          </button>
          {Array.from({ length: totalPages }, (_, i) => (
            <button
              key={i + 1}
              onClick={() => setCurrentPage(i + 1)}
              className={cn(
                "px-3 py-1 border rounded text-sm",
                currentPage === i + 1 && "bg-blue-500 text-white"
              )}
            >
              {i + 1}
            </button>
          ))}
          <button
            onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded text-sm"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
