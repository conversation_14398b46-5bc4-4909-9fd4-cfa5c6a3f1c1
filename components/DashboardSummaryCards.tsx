/**
 * DashboardSummaryCards Container Component
 *
 * A container component that manages multiple SummaryCard components
 * with different layout options and responsive behavior.
 *
 * Features:
 * - Multiple layout options (horizontal, grid-2x3, grid-3x2, auto)
 * - Responsive design that adapts to screen sizes
 * - Support for different card sizes
 * - RTL support
 * - Loading states
 */

import React from "react";
import SummaryCard, { SummaryCard as SummaryCardType } from "./SummaryCard";
import { useLanguage } from "../contexts/LanguageContext";

export interface DashboardSummaryCardsProps {
  cards: SummaryCardType[];
  layout?: "horizontal" | "grid-2x3" | "grid-3x2" | "auto";
  showTrends?: boolean;
  className?: string;
  cardSize?: "sm" | "md" | "lg";
}

// Layout styles for different configurations
const layoutStyles = {
  horizontal: {
    container: "flex flex-col sm:flex-row gap-4 overflow-x-auto pb-2",
    card: "flex-shrink-0 min-w-[280px] sm:flex-1",
  },
  "grid-2x3": {
    container:
      "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4",
    card: "w-full",
  },
  "grid-3x2": {
    container:
      "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-4",
    card: "w-full",
  },
  auto: {
    container:
      "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4",
    card: "w-full",
  },
};

export default function DashboardSummaryCards({
  cards,
  layout = "auto",
  showTrends = false,
  className = "",
  cardSize = "sm",
}: DashboardSummaryCardsProps) {
  const { dir } = useLanguage();
  const layoutStyle = layoutStyles[layout];

  // Handle empty cards array
  if (!cards || cards.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500">No summary data available</p>
      </div>
    );
  }

  return (
    <div
      className={`
        ${layoutStyle.container}
        ${dir === "rtl" ? "rtl" : "ltr"}
        ${layout === "horizontal" ? "snap-x snap-mandatory" : ""}
        ${className}
      `}
      role="region"
      aria-label="Dashboard Summary Cards"
    >
      {cards.map((card, index) => (
        <div
          key={card.id}
          className={`
            ${layoutStyle.card}
            ${layout === "horizontal" ? "snap-start" : ""}
            ${layout === "horizontal" && index === 0 ? "ml-0" : ""}
            ${
              layout === "horizontal" && index === cards.length - 1
                ? "mr-0"
                : ""
            }
            transition-all duration-200 ease-out
          `}
        >
          <SummaryCard
            {...card}
            size={cardSize}
            showTrends={showTrends}
            className={`
              h-full
              ${layout === "horizontal" ? "min-h-[120px]" : ""}
              card-smooth-hover
            `}
          />
        </div>
      ))}
    </div>
  );
}

// Export types for external use
export type { SummaryCardType };
