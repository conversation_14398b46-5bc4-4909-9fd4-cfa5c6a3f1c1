'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { TripAlertsTab } from './tabs/TripAlertsTab';
import { MovementReportTab } from './tabs/MovementReportTab';
import { TripEventsReportTab } from './tabs/TripEventsReportTab';
import { TripPingsTab } from './tabs/TripPingsTab';
import { TripActivitiesReportTab } from './tabs/TripActivitiesReportTab';
import { TripViewsDisplayTab } from './tabs/TripViewsDisplayTab';
import { AlertViewsDisplayTab } from './tabs/AlertViewsDisplayTab';
import TripAlertMapViewer from '@/components/TripAlertMapViewer';
import tripAlertsData from "../../../data/trip_alerts.json";
import { TripAlert } from '@/types/trip_alert';

const tabs = [
  { 
    id: 'trip_map', 
    label: 'TRIP MAP',
    icon: '🗺️'
  },
  { 
    id: 'trip_alerts', 
    label: 'TRIP ALERTS',
    icon: '🔔'
  },
  { 
    id: 'trip_pings', 
    label: 'TRIP PINGS',
    icon: '🧮'
  },
  { 
    id: 'movement_report', 
    label: 'MOVEMENT REPORT',
    icon: '📋'
  },
  { 
    id: 'trip_activities_report', 
    label: 'TRIP ACTIVITIES REPORT',
    icon: '📋'
  },
  { 
    id: 'trip_events_report', 
    label: 'TRIP EVENTS REPORT',
    icon: '📋'
  },
  { 
    id: 'trip_views_display', 
    label: 'TRIP VIEWS DISPLAY',
    icon: '👁️'
  },
  { 
    id: 'alert_views_display', 
    label: 'ALERT VIEWS DISPLAY',
    icon: '👁️'
  }
];

interface TripTabsComponentProps {
  className?: string;
}

const tripAlerts = tripAlertsData.tripAlerts as TripAlert[];

export const TripTabsComponent: React.FC<TripTabsComponentProps> = ({
  className = ''
}) => {
  const [selectedTripId, setSelectedTripId] = useState<string | undefined>(undefined);
  const [selectedAlert, setSelectedAlert] = useState<TripAlert | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('current_tab') || 'trip_map';

  const handleTabChange = (tabId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set('current_tab', tabId);
    router.push(`?${params.toString()}`);
  };

  const handleAlertClick = (alert: TripAlert) => {
      setSelectedAlert(alert);
      console.log("Alert clicked:", alert);
    };

  // const handleTripFilter = (tripId: string | undefined) => {
  //   setSelectedTripId(tripId);
  //   setSelectedAlert(null);
  // };

  const TripAlertMapViewerTab = () => {
    return (
      /* Map Viewer Section */
       <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <TripAlertMapViewer
            tripAlerts={tripAlerts}
            selectedTripId={selectedTripId}
            onAlertClick={handleAlertClick}
            className="h-[600px]"
          />
         </div> 
    )
  }
  
  

  const renderTabContent = () => {
    switch (currentTab) {
      case 'trip_map':
        return <TripAlertMapViewerTab/>;
      case 'trip_alerts':
        return <TripAlertsTab />;
      case 'trip_pings':
        return <TripPingsTab />;
      case 'movement_report':
        return <MovementReportTab />;
      case 'trip_activities_report':
        return <TripActivitiesReportTab />;
      case 'trip_events_report':
        return <TripEventsReportTab />;
      case 'trip_views_display':
        return <TripViewsDisplayTab />;
      case 'alert_views_display':
        return <AlertViewsDisplayTab />;
      default:
        return <TripAlertMapViewerTab/>;
    }
  };

  return (
    <Card className={className}>
      {/* Tab Navigation */}
      <div className="flex border-b bg-gray-50">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`px-4 py-2 text-sm font-medium transition-colors flex items-center gap-1 ${
              currentTab === tab.id
                ? 'text-blue-500 border-b-2 border-blue-500'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <CardContent className="p-6 h-96">
        {renderTabContent()}
      </CardContent>
    </Card>
  );
};
