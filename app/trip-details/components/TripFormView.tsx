'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trip } from '@/types/trip';

interface TripFormViewProps {
  trip?: Trip | null;
  showActions?: boolean;
  onTrackTrip?: (trip: Trip) => void;
  onStopTrip?: () => void;
  onEditSecurityNotes?: () => void;
  className?: string;
}

export const TripFormView: React.FC<TripFormViewProps> = ({
  trip,
  showActions = true,
  onTrackTrip,
  onStopTrip,
  onEditSecurityNotes,
  className = ''
}) => {
  if (!trip) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="text-center text-gray-500">Trip not found</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <CardTitle className="text-xl font-bold">Trip Details</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 gap-4 text-sm">
          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Transit Number:</span>
            <span className="font-medium">{trip.transitNumber}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Transit Type:</span>
            <span className="font-medium">{trip.transitType}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Declaration Date:</span>
            <span className="font-medium">{trip.declarationDate || 'N/A'}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Transit Seq No:</span>
            <span className="font-medium">{trip.transitSeqNo || 'N/A'}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Exit Date:</span>
            <span className="font-medium">{trip.endDate || 'N/A'}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Tracker No:</span>
            <span className="font-medium">{trip.vehicle_details.trackerNo}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Elocks:</span>
            <span className="font-medium">{trip.tracking.elocks}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">General Details:</span>
            <span className="font-medium">
              {trip.route.entryPort} : {trip.route.exitPort}
            </span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Driver Name:</span>
            <span className="font-medium">{trip.driver_details.driverName}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Driver Passport Number:</span>
            <span className="font-medium">{trip.driver_details.driverPassportNumber}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Driver Nationality:</span>
            <span className="font-medium">{trip.driver_details.driverNationality}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Driver Contact No:</span>
            <span className="font-medium">{trip.driver_details.driverContactNo || 'N/A'}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Security Notes:</span>
            <div className="flex flex-col items-end">
              <span className="font-medium">{trip.compliance.securityNotes}</span>
              <Button 
                variant="destructive" 
                size="sm"
                className="mt-2"
                onClick={onEditSecurityNotes}
              >
                Edit Security Notes
              </Button>
            </div>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Remaining Distance:</span>
            <span className="font-medium">{trip.tracking.remainingDistance} km</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Status:</span>
            <span className="font-medium capitalize">{trip.tripStatus}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Goods Description:</span>
            <span className="font-medium">{trip.shipment.shipmentDescription}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Owner Description:</span>
            <span className="font-medium">{trip.shipment.ownerDescription || 'N/A'}</span>
          </div>

          <div className="flex justify-between border-b pb-2">
            <span className="text-gray-600">Goods Description (Arabic):</span>
            <span className="font-medium">{trip.shipment.shipmentDescriptionArabic || 'وصف البضائع المنقولة'}</span>
          </div>
        </div>

        {showActions && (
          <div className="flex gap-4 justify-center mt-6">
            <Button 
              className="bg-green-600 hover:bg-green-700 text-white px-6"
              onClick={() => onTrackTrip?.(trip)}
            >
              Track Trip
            </Button>
            <Button 
              variant="destructive" 
              className="px-6"
              onClick={onStopTrip}
            >
              Stop Trip
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
