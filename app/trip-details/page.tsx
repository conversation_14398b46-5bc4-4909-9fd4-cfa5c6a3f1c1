'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import { TripFormView } from './components/TripFormView';
import { TripTabsComponent } from './components/TripTabsComponent';
import { useTripData } from '@/hooks/useTripData';

export default function TripDetailsPage() {
  const searchParams = useSearchParams();
  const tripId = searchParams.get('trip_id') || 'TRP001';
  const { tripData, loading, error } = useTripData(tripId);

  if (loading) {
    return (
      <div className="mx-auto py-8">
        <div className="text-center">Loading trip data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto py-8">
        <div className="text-center text-red-500">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="mx-auto py-4 px-4 ">
      <div className="flex gap-6 h-full">
        {/* Left Side - Trip Form */}
        <div className="w-1/4">
          <TripFormView
            trip={tripData}
            showActions={true}
            onTrackTrip={(trip) => console.log('Track trip:', trip.tripId)}
            onStopTrip={() => console.log('Stop trip')}
            onEditSecurityNotes={() => {
              const securityNotes = prompt('Enter security notes:');
              if (securityNotes) {
                console.log('Security notes updated:', securityNotes);
              }
            }}
          />
        </div>

        {/* Right Side - Tabs */}
        <TripTabsComponent className="flex-1" />
      </div>
    </div>
  );
}
