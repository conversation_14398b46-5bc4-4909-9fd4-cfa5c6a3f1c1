'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { TripDetail } from '../../types/trip';

interface TripFormComponentProps {
  trip?: TripDetail;
  showActions?: boolean;
  onTrackTrip?: (trip: TripDetail) => void;
  onStopTrip?: () => void;
  onEditSecurityNotes?: () => void;
  className?: string;
}

export const TripFormComponent: React.FC<TripFormComponentProps> = ({
  trip,
  showActions = true,
  onTrackTrip,
  onStopTrip,
  onEditSecurityNotes,
  className = ''
}) => {
  if (!trip) {
    return <div className="p-4">Trip not found</div>;
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  return (
    <div className={className}>
      <Card className="w-full">
        <CardHeader className="text-center">
          <CardTitle className="text-xl font-bold">Trip Details</CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className=" text-sm">
            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Transit Number :</span>
              <span className="font-medium">{trip.tripNumber || '0'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Transit Type :</span>
              <span className="font-medium">{trip.transitType || 'ImportTypeName'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Declaration Date :</span>
              <span className="font-medium">{formatDate(trip.plannedStartTime)}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Transit Seq No. :</span>
              <span className="font-medium">{trip.transitSeqNo || 'V123'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Owner Description :</span>
              <span className="font-medium">{trip.cargo?.ownerDescription || 'OwnerDescription'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Shipment Description :</span>
              <span className="font-medium">{trip.cargo?.description || 'ShipmentDescription'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Entry Port :</span>
              <span className="font-medium">{trip.origin.name}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Exit Port :</span>
              <span className="font-medium">{trip.destination.name}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Starting Date :</span>
              <span className="font-medium">{formatDate(trip.plannedStartTime)}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Expected Arrival Date :</span>
              <span className="font-medium">{trip.estimatedArrival ? formatDate(trip.estimatedArrival) : formatDate(trip.plannedEndTime)}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">End Date :</span>
              <span className="font-medium">{trip.actualEndTime ? formatDate(trip.actualEndTime) : '-'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Tracker No :</span>
              <span className="font-medium">{trip.vehicle.trackerId}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Elocks :</span>
              <span className="font-medium">{trip.vehicle.elocks || '15627132'}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Vehicle Details :</span>
              <span className="font-medium text-blue-600">
                {trip.vehicle.plateNumber}, {trip.vehicle.make}, {trip.vehicle.model}, {trip.vehicle.year}, {trip.vehicle.vehicleType}
              </span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Driver Name :</span>
              <span className="font-medium">{trip.driver.name}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Driver Passport Number :</span>
              <span className="font-medium text-blue-600">{trip.driver.passportNumber}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Driver Nationality :</span>
              <span className="font-medium">{trip.driver.nationality}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Driver Contact No. :</span>
              <span className="font-medium">{trip.driver.contactNumber}</span>
            </div>

            <div className="flex justify-between border-b pb-2 col-span-2">
              <span className="text-gray-600">Security Notes :</span>
              <span className='flex flex-col'>
                <span className="font-medium">SecurityNotes</span>
                <Button 
                variant="destructive" 
                className="px-6"
                onClick={onEditSecurityNotes}
              >
                Edit Security Notes
              </Button>
              </span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Complete Distance :</span>
              <span className="font-medium">{trip.completedDistance} kilometers</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="text-gray-600">Remaining Distance :</span>
              <span className="font-medium">{trip.totalDistance - trip.completedDistance} kilometers</span>
            </div>

            <div className="flex justify-between border-b pb-2 col-span-2">
              <span className="text-gray-600">Status :</span>
              <span className="font-medium">{trip.trackingStatus || 'Activated • Target in Entry Border'}</span>
            </div>
          </div>

          {/* Action Buttons */}
          {showActions && (
            <div className="flex gap-4 justify-center mt-6">
              <Button 
                className="bg-green-600 hover:bg-green-700 text-white px-6"
                onClick={() => onTrackTrip?.(trip)}
              >
                Track Trip
              </Button>
              <Button 
                variant="destructive" 
                className="px-6"
                onClick={onStopTrip}
              >
                Stop Trip
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
