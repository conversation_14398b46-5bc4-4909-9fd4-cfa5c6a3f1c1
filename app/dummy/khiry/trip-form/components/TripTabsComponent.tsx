'use client';

import React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent } from './ui/card';
import { TripMapTab } from './tabs/TripMapTab';
import { TripAlertsTab } from './tabs/TripAlertsTab';
import { TripImgsTab } from './tabs/TripImgsTab';
import { MovementReportTab } from './tabs/MovementReportTab';
import { TripActivitiesBudgetTab } from './tabs/TripActivitiesBudgetTab';
import { TripEventsReportTab } from './tabs/TripEventsReportTab';

const tabs = [
  { id: 'trip_map', label: 'TRIP MAP', labelAr: 'خريطة الرحلة' },
  { id: 'trip_alerts', label: 'TRIP ALERTS', labelAr: 'تنبيهات الرحلة' },
  { id: 'trip_imgs', label: 'TRIP IMGS', labelAr: 'صور الرحلة' },
  { id: 'movement_report', label: 'MOVEMENT REPORT', labelAr: 'تقرير الحركة' },
  { id: 'trip_activities_budget', label: 'TRIP ACTIVITIES BUDGET', labelAr: 'ميزانية أنشطة الرحلة' },
  { id: 'trip_events_report', label: 'TRIP EVENTS REPORT', labelAr: 'تقرير أحداث الرحلة' }
];

interface TripTabsComponentProps {
  className?: string;
}

export const TripTabsComponent: React.FC<TripTabsComponentProps> = ({
  className = ''
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('current_tab') || 'trip_map';

  const handleTabChange = (tabId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set('current_tab', tabId);
    router.push(`?${params.toString()}`);
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 'trip_map':
        return <TripMapTab />;
      case 'trip_alerts':
        return <TripAlertsTab />;
      case 'trip_imgs':
        return <TripImgsTab />;
      case 'movement_report':
        return <MovementReportTab />;
      case 'trip_activities_budget':
        return <TripActivitiesBudgetTab />;
      case 'trip_events_report':
        return <TripEventsReportTab />;
      default:
        return <TripMapTab />;
    }
  };

  return (
    <Card className={className}>
      {/* Tab Navigation */}
      <div className="flex border-b bg-gray-50">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              currentTab === tab.id
                ? 'text-blue-500 border-b-2 border-blue-500'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <CardContent className="p-6 h-96">
        {renderTabContent()}
      </CardContent>
    </Card>
  );
};
