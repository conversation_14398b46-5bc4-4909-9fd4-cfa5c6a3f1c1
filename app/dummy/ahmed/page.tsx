"use client";

import React from "react";
import InteractiveMapContainer from "../../../components/interactive-map/InteractiveMapContainer";
import { Location, Alert, Route } from "../../../types/map";

export default function AhmedTestPage() {
  // Event handlers for <PERSON>'s dummy testing
  const handleLocationClick = (location: Location) => {
    console.log("Ahmed Dummy - Location clicked:", location);
    // Add <PERSON>'s test location handling
  };

  const handleAlertClick = (alert: Alert) => {
    console.log("<PERSON> Dummy - Alert clicked:", alert);
    // Add <PERSON>'s test alert handling
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log("Ahmed Dummy - Routes selected:", routes);
    // Add <PERSON>'s test route handling
  };

  return (
    <div className="w-full" style={{ height: "calc(100vh - 112px - 58px)" }}>
      {/* Header for <PERSON>'s test page */}
      <div className="absolute top-4 left-4 z-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <h1 className="text-lg font-bold text-gray-800">
          <PERSON>&apos;s Test Page
        </h1>
        <p className="text-sm text-gray-600">
          صفحة اختبار أحمد - Dummy Data Testing
        </p>
      </div>

      <InteractiveMapContainer
        pageType="dummy-ahmed"
        initialCenter={{ lat: 24.7136, lng: 46.6753 }}
        initialZoom={6}
        showSidebar={true}
        onLocationClick={handleLocationClick}
        onAlertClick={handleAlertClick}
        onRouteSelect={handleRouteSelect}
      />
    </div>
  );
}
