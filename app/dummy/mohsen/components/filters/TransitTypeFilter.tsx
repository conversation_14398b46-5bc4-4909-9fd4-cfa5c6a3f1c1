'use client';
import React, { useState } from 'react';

const TRANSIT_OPTIONS = [
  "Loaded Transit", "Statistical Declaration", "Import Customs Declaration",
  "Passengers Transit", "Gulf Destination", "Export Customs Declaration",
  "Empty Truck Transit", "Internal Transit", "Warehouse Transfer"
];

interface TransitTypeFilterProps {
  selected: string[];
  onToggle: (option: string) => void;
  onSelectAll: (options: string[]) => void;
}

export default function TransitTypeFilter({
  selected = [],
  onToggle,
  onSelectAll,
}: TransitTypeFilterProps) {
  const [expanded, setExpanded] = useState(false);
  const allSelected = selected.length === TRANSIT_OPTIONS.length;

  const toggleAll = () => {
    onSelectAll(allSelected ? [] : [...TRANSIT_OPTIONS]);
  };

  return (
    <div className="border border-gray-200 rounded mb-4">
      <button
        type="button"
        onClick={() => setExpanded((e) => !e)}
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
      >
        <span className="font-medium"> Transit Type</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${
            expanded ? '' : 'rotate-180'
          }`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {expanded && (
        <div className="p-4 text-sm text-gray-700 space-y-2">
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={allSelected}
                onChange={toggleAll}
              />
              <span> Select All </span>
            </label>
          </div>
          {TRANSIT_OPTIONS.map((option) => (
            <label key={option} className="block">
              <input
                type="checkbox"
                checked={selected.includes(option)}
                onChange={() => onToggle(option)}
                className="mr-2"
              />
              {option}
            </label>
          ))}
        </div>
      )}
    </div>
  );
}
