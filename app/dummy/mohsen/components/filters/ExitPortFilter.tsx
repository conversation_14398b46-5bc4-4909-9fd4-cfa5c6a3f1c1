"use client";
import React, { useState } from "react";

interface ExitPortFilterProps {
  ports: string[];
  selected: string[];
  onToggle: (port: string) => void;
  onSelectAll: (ports: string[]) => void;
}

export default function ExitPortFilter({
  ports,
  selected,
  onToggle,
  onSelectAll,
}: ExitPortFilterProps) {
  const [expanded, setExpanded] = useState(false);
  const allSelected = selected.length === ports.length;
  const indeterminate = selected.length > 0 && selected.length < ports.length;

  return (
    <div className="border border-gray-200 rounded mb-4">
      <button
        type="button"
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
        onClick={() => setExpanded((e) => !e)}
      >
        <span className="font-medium">Exit Port</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${
            expanded ? "" : "rotate-180"
          }`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {expanded && (
        <div className="px-4 py-3 grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
          <label className="flex items-center col-span-full">
            <input
              type="checkbox"
              className="form-checkbox h-4 w-4 text-blue-600"
              checked={allSelected}
              ref={(el) => {
                if (el) el.indeterminate = indeterminate;
              }}
              onChange={() => onSelectAll(allSelected ? [] : ports)}
            />
            <span className="ml-2 text-sm">Select All</span>
          </label>

          {ports.map((port) => (
            <label key={port} className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-blue-600"
                checked={selected.includes(port)}
                onChange={() => onToggle(port)}
              />
              <span className="ml-2 text-sm">{port}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}
