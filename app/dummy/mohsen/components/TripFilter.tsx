"use client";
import { useState, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";

import EntryPortFilter from "./filters/EntryPortFilter";
import ExitPortFilter from "./filters/ExitPortFilter";
import AlertsFilter from "./filters/AlertsFilter";
import TruckInfoFilter from "./filters/TruckInfoFilter";
import DatesFilter from "./filters/DatesFilter";
import OrderByFilter from "./filters/OrderByFilter";
import ShipmentDescFilter from "./filters/ShipmentDescFilter";
import TransitTypeFilter from "./filters/TransitTypeFilter";
import CreatedRangeFilter from "./filters/CreatedRangeFilter";
import portsData from "../data/ports.json";

const PORTS: string[] = portsData.ports.map((p: { locationName: string }) => p.locationName);

interface TruckInfo {
  transitNo: string;
  seqNo: string;
  driverName: string;
  plate: string;
  tracker: string;
  tripCode: string;
  priority: string[];
  status: string[];
  location: string[];
}

interface Dates {
  transitDate: string;
  entryFrom: string;
  entryTo: string;
}

interface OrderBy {
  direction: "asc" | "desc";
  field: string;
}

interface CreatedRange {
  createdAtStart: string;
  createdAtEnd: string;
}

interface TripFilterProps {
  onSearch: (filters: {
    entryPorts: string[];
    exitPorts: string[];
    selectedAlerts: string[];
    truckInfo: TruckInfo;
    dates: Dates;
    orderBy: OrderBy;
    shipmentDesc: string[];
    transitTypes: string[];
    createdRange: CreatedRange;
  }) => void;
}

export default function TripFilter({ onSearch }: TripFilterProps) {
  const [isOpen, setIsOpen] = useState(false);

  const [entryPorts, setEntryPorts] = useState<string[]>([]);
  const [exitPorts, setExitPorts] = useState<string[]>([]);
  const [selectedAlerts, setSelectedAlerts] = useState<string[]>([]);
  const [truckInfo, setTruckInfo] = useState<TruckInfo>({
    transitNo: "",
    seqNo: "",
    driverName: "",
    plate: "",
    tracker: "",
    tripCode: "",
    priority: [],
    status: [],
    location: [],
  });
  const [dates, setDates] = useState<Dates>({
    transitDate: "",
    entryFrom: "",
    entryTo: "",
  });
  const [orderBy, setOrderBy] = useState<OrderBy>({
    direction: "asc",
    field: "Created Date",
  });
  const [shipmentDesc, setShipmentDesc] = useState<string[]>([]);
  const [transitTypes, setTransitTypes] = useState<string[]>([]);
  const [createdRange, setCreatedRange] = useState<CreatedRange>({
    createdAtStart: "",
    createdAtEnd: "",
  });

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="fixed top-4 left-4 z-50 bg-blue-600 text-white px-4 py-2 rounded shadow"
      >
        Show Filters
      </button>

      <Transition show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={setIsOpen}>
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm"
            aria-hidden="true"
          />

          <div className="fixed inset-0 flex items-center justify-center p-4">
            <Dialog.Panel className="w-full max-w-md bg-white rounded-lg shadow-xl p-4 max-h-[90vh] overflow-y-auto">
              <Dialog.Title className="text-lg font-bold text-gray-800 mb-2">
                Trip Filters
              </Dialog.Title>

              <EntryPortFilter
                ports={PORTS}
                selected={entryPorts}
                onToggle={(p) =>
                  setEntryPorts((prev) =>
                    prev.includes(p)
                      ? prev.filter((x) => x !== p)
                      : [...prev, p]
                  )
                }
                onSelectAll={(all) => setEntryPorts(all)}
              />
              <ExitPortFilter
                ports={PORTS}
                selected={exitPorts}
                onToggle={(p) =>
                  setExitPorts((prev) =>
                    prev.includes(p)
                      ? prev.filter((x) => x !== p)
                      : [...prev, p]
                  )
                }
                onSelectAll={(all) => setExitPorts(all)}
              />
              <AlertsFilter
                selected={selectedAlerts}
                onToggle={(opt) =>
                  setSelectedAlerts((prev) =>
                    prev.includes(opt)
                      ? prev.filter((x) => x !== opt)
                      : [...prev, opt]
                  )
                }
              />
              <TruckInfoFilter value={truckInfo} onChange={setTruckInfo} />
              <DatesFilter value={dates} onChange={setDates} />
              <OrderByFilter value={orderBy} onChange={setOrderBy} />
              <ShipmentDescFilter
                selected={shipmentDesc}
                onToggle={(s) =>
                  setShipmentDesc((prev) =>
                    prev.includes(s)
                      ? prev.filter((x) => x !== s)
                      : [...prev, s]
                  )
                }
                onSelectAll={(all) => setShipmentDesc(all)}
              />

              <TransitTypeFilter
                selected={transitTypes}
                onToggle={(t) =>
                  setTransitTypes((prev) =>
                    prev.includes(t)
                      ? prev.filter((x) => x !== t)
                      : [...prev, t]
                  )
                }
                onSelectAll={(all) => setTransitTypes(all)}
              />

              <CreatedRangeFilter
                value={createdRange}
                onChange={(value) =>
                  setCreatedRange({
                    createdAtStart: value.createdAtStart || "",
                    createdAtEnd: value.createdAtEnd || "",
                  })
                }
              />

              <div className="mt-4 flex space-x-2">
                <button
                  className="flex-1 bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
                  onClick={() => {
                    onSearch({
                      entryPorts,
                      exitPorts,
                      selectedAlerts,
                      truckInfo,
                      dates,
                      orderBy,
                      shipmentDesc,
                      transitTypes,
                      createdRange,
                    });
                    setIsOpen(false);
                  }}
                >
                  SEARCH
                </button>
                <button
                  className="flex-1 border border-gray-300 text-gray-700 py-2 rounded hover:bg-gray-100"
                  onClick={() => window.location.reload()}
                >
                  RESET
                </button>
              </div>
            </Dialog.Panel>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
