"use client";

import { DataTable } from "@/components/shared/DataTable";
import TripMasterDetailView from "@/components/TripMasterDetailView";
import {
  BatteryIcon,
  CheckIcon,
  PowerOff,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Trip } from "@/types/trip";

interface LocalTrip {
  id: number;
  transitNumber: number;
  description: string;
  entry: string;
  lastSeen: string;
  tracker: string;
  driver: string;
  vehicle: string;
  alerts: string;
  status: ("active" | "charging" | "offline")[];
}

const statusConfig: Record<
  LocalTrip["status"][number],
  {
    color: string;
    icon: React.ReactNode;
  }
> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <PowerOff className="text-red-500" size={16} />,
  },
};

export default function SabreenPage() {
  const [data, setData] = useState<LocalTrip[]>([]);

  useEffect(() => {
    fetch("/data/trips.json")
      .then((res) => res.json())
      .then(setData)
      .catch(console.error);
  }, []);

  return (
    <div className="bg-gray-100 min-h-screen p-6 space-y-2 ">

      <DataTable<LocalTrip>
        keyField="id"
        data={data}
        stickyHeader={true}
        expandable={true}
        expandRowByClick={false}
        pageSize={10}
        filterable={true}
        searchable={true}
        sortable={true}
        selectable={true}
        exportable={true}
        exportFormats={["xlsx", "csv", "pdf"]}
        exportFileNameKey="trip-data"
        columns={[
          
          {
            key: "transitNumber",
            title: "Transit Number",
            render: (row) => (
              <span className="text-blue-400 font-medium">
                {row.transitNumber}
              </span>
            ),
          },
          { key: "description", title: "Shipment Description" },
          { key: "entry", title: "Entry-port - Exit-port" },
          { key: "lastSeen", title: "Last Seen" },
          { key: "tracker", title: "Tracker" },
          { key: "driver", title: "Driver Name" },
          { key: "vehicle", title: "Vehicle" },
          { key: "alerts", title: "Alerts" },
          {
            key: "status",
            title: "Trip Status",
            render: (row) => (
              <div className="flex gap-1 items-center">
                {row.status?.map((s, idx) => (
                  <span key={idx}>{statusConfig[s]?.icon}</span>
                ))}
              </div>
            ),
          },
        ]}
        expandedRowRender={(row) => {
          // Convert LocalTrip to Trip format for TripMasterDetailView
          const tripData: Trip = {
            id: row.id,
            tripId: row.id.toString(),
            transitNumber: row.transitNumber,
            description: row.description,
            entry: row.entry,
            lastSeen: row.lastSeen,
            tracker: row.tracker,
            driver: row.driver,
            vehicle: row.vehicle,
            alerts: row.alerts,
            status: row.status,
            tripStatus: 'activated',
            transitType: 'commercial',
            creationDate: new Date().toISOString(),
            activationDate: new Date().toISOString(),
            completionDate: null,
            endDate: undefined,
            driver_details: {
              driverId: row.id.toString(),
              driverName: row.driver,
              driverPassportNumber: '',
              driverNationality: ''
            },
            vehicle_details: {
              vehicleId: row.id.toString(),
              vehiclePlateNumber: row.vehicle,
              trackerNo: row.tracker
            },
            route: {
              routeId: row.id.toString(),
              routeName: row.entry,
              entryPort: row.entry.split(' - ')[0] || row.entry,
              exitPort: row.entry.split(' - ')[1] || row.entry
            },
            shipment: {
              shipmentId: row.id.toString(),
              shipmentDescription: row.description
            },
            tracking: {
              currentLocation: {
                latitude: 0,
                longitude: 0,
                timestamp: new Date().toISOString()
              },
              completeDistance: 0,
              remainingDistance: 0,
              estimatedArrival: null,
              elocks: ''
            },
            compliance: {
              securityNotes: '',
              customsStatus: 'pending',
              documentStatus: 'pending'
            }
          };
          return <TripMasterDetailView row={tripData} />;
        }}
      />
    </div>
  );
}
