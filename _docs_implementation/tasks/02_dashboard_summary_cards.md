# Task 02: Dashboard Summary Cards Component

## Assigned to : Safa

## User Story
**As a** user viewing different dashboard pages  
**I want** to see consistent summary cards with key metrics  
**So that** I can quickly understand important statistics across all pages

## Acceptance Criteria

### Given: Summary Card Requirements
- [ ] Cards should display key metrics with icons and values
- [ ] Cards should be configurable for different layouts
- [ ] Cards should support internationalization

### When: Using Summary Cards
- [ ] My Assigned Ports page shows 4 cards (clock + 4 statistics)
- [ ] Location Monitor page shows 4 cards (statistics only)
- [ ] Dashboard Overview shows 6+ cards (comprehensive stats)
- [ ] Suspicious Trips page shows 4 cards (risk-focused metrics)

### Then: Cards Should
- [ ] Display consistently across all pages
- [ ] Support different color themes based on metric type
- [ ] Be responsive on mobile devices
- [ ] Load efficiently without performance issues

## Technical Requirements

### SummaryCard Interface
```typescript
interface SummaryCard {
  id: string;
  titleKey: string; // Translation key instead of hardcoded text
  value: number | string;
  icon: 'clock' | 'bell-orange' | 'bell-gray' | 'check' | 'prohibition' | 'custom';
  customIcon?: React.ReactNode;
  color: 'blue' | 'orange' | 'gray' | 'green' | 'red';
  onClick?: () => void;
  loading?: boolean;
}

interface DashboardSummaryCardsProps {
  cards: SummaryCard[];
  layout?: 'horizontal' | 'grid-2x3' | 'grid-3x2' | 'auto';
  showTrends?: boolean;
  className?: string;
  cardSize?: 'sm' | 'md' | 'lg';
}
```

### Icon System
- [ ] Implement icon mapping for predefined icons
- [ ] Support custom React components as icons
- [ ] Ensure icons are accessible with proper ARIA labels

### Layout Options
- [ ] **Horizontal**: Single row layout for mobile
- [ ] **Grid 2x3**: 2 rows, 3 columns for desktop
- [ ] **Grid 3x2**: 3 rows, 2 columns for tablet
- [ ] **Auto**: Responsive grid that adapts to screen size

## Page-Specific Configurations

### My Assigned Ports (5 cards)
```typescript
const assignedPortsCards: SummaryCard[] = [
  { id: 'clock', titleKey: 'current_time', icon: 'clock', color: 'blue' },
  { id: 'total', titleKey: 'total_ports', icon: 'check', color: 'green' },
  { id: 'active', titleKey: 'active_trips', icon: 'bell-orange', color: 'orange' },
  { id: 'completed', titleKey: 'completed_today', icon: 'check', color: 'green' },
  { id: 'pending', titleKey: 'pending_review', icon: 'bell-gray', color: 'gray' }
];
```

### Location Monitor (4 cards)
```typescript
const locationMonitorCards: SummaryCard[] = [
  { id: 'online', titleKey: 'vehicles_online', icon: 'check', color: 'green' },
  { id: 'offline', titleKey: 'vehicles_offline', icon: 'prohibition', color: 'red' },
  { id: 'alerts', titleKey: 'active_alerts', icon: 'bell-orange', color: 'orange' },
  { id: 'geofence', titleKey: 'geofence_violations', icon: 'bell-gray', color: 'gray' }
];
```

## Definition of Done
- [ ] DashboardSummaryCards component implemented with TypeScript
- [ ] All layout options working responsively
- [ ] Icon system implemented and tested
- [ ] Component documented with Storybook
- [ ] Accessibility testing passed
- [ ] Integration tested on all target pages


## Notes
- Consider animation for hover effects
- Ensure cards work well with the PageLayout component
- Plan for real-time data updates

