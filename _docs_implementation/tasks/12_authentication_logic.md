# Task 12: Authentication Logic Implementation

## User Story
**As a** system administrator  
**I want** to implement secure authentication logic  
**So that** users can log in safely and access protected resources

## Acceptance Criteria

### Given: Authentication System Requirements
- [ ] System should validate user credentials securely
- [ ] System should manage user sessions properly
- [ ] System should handle authentication errors gracefully
- [ ] System should support token-based authentication

### When: User Attempts Authentication
- [ ] System validates credentials against backend
- [ ] System creates secure session upon successful login
- [ ] System redirects user to appropriate page after login
- [ ] System handles failed login attempts appropriately

### Then: Authentication Should
- [ ] Maintain secure user sessions
- [ ] Provide clear feedback on authentication status
- [ ] Handle token refresh automatically
- [ ] Log security events appropriately
- [ ] Support logout functionality

## Authentication Architecture

### Authentication Flow
```typescript
interface AuthenticationFlow {
  1: 'User submits credentials';
  2: 'Client validates input format';
  3: 'Client sends credentials to server';
  4: 'Server validates credentials';
  5: 'Server returns JWT tokens';
  6: 'Client stores tokens securely';
  7: 'Client redirects to dashboard';
}

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  permissions: string[];
  lastLogin: Date;
  isActive: boolean;
}
```

### Authentication Context
```typescript
interface AuthContextType {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  
  // Utilities
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  getToken: () => string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### Authentication Provider
```typescript
interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = getStoredToken();
      if (token && !isTokenExpired(token)) {
        const user = await validateToken(token);
        setState(prev => ({
          ...prev,
          user,
          isAuthenticated: true,
          isLoading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
        }));
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Authentication initialization failed',
      }));
    }
  };

  const login = async (credentials: LoginCredentials) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const response = await authAPI.login(credentials);
      const { user, tokens } = response.data;
      
      // Store tokens securely
      storeTokens(tokens);
      
      // Update state
      setState(prev => ({
        ...prev,
        user,
        isAuthenticated: true,
        isLoading: false,
      }));
      
      // Log successful login
      logSecurityEvent('LOGIN_SUCCESS', { userId: user.id });
      
    } catch (error) {
      const errorMessage = getErrorMessage(error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      
      // Log failed login attempt
      logSecurityEvent('LOGIN_FAILED', { 
        username: credentials.username,
        error: errorMessage 
      });
    }
  };

  const logout = () => {
    // Clear stored tokens
    clearStoredTokens();
    
    // Reset state
    setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    
    // Log logout
    if (state.user) {
      logSecurityEvent('LOGOUT', { userId: state.user.id });
    }
    
    // Redirect to login
    window.location.href = '/login';
  };

  const refreshToken = async () => {
    try {
      const refreshToken = getStoredRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }
      
      const response = await authAPI.refreshToken(refreshToken);
      const { tokens } = response.data;
      
      storeTokens(tokens);
      
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  const hasPermission = (permission: string): boolean => {
    return state.user?.permissions.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    return state.user?.roles.includes(role) || false;
  };

  const getToken = (): string | null => {
    return getStoredToken();
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    refreshToken,
    clearError,
    hasPermission,
    hasRole,
    getToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

## Token Management

### Secure Token Storage
```typescript
interface TokenStorage {
  store: (tokens: AuthTokens) => void;
  retrieve: () => AuthTokens | null;
  clear: () => void;
  isExpired: (token: string) => boolean;
}

// Secure token storage implementation
class SecureTokenStorage implements TokenStorage {
  private readonly ACCESS_TOKEN_KEY = 'auth_access_token';
  private readonly REFRESH_TOKEN_KEY = 'auth_refresh_token';
  
  store(tokens: AuthTokens): void {
    // Store in httpOnly cookie for production
    if (typeof window !== 'undefined') {
      // Development: localStorage (not recommended for production)
      localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.accessToken);
      localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refreshToken);
      
      // Production: Use httpOnly cookies via API call
      // this.storeInHttpOnlyCookie(tokens);
    }
  }
  
  retrieve(): AuthTokens | null {
    if (typeof window === 'undefined') return null;
    
    const accessToken = localStorage.getItem(this.ACCESS_TOKEN_KEY);
    const refreshToken = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    
    if (!accessToken || !refreshToken) return null;
    
    return {
      accessToken,
      refreshToken,
      expiresIn: this.getTokenExpiration(accessToken),
      tokenType: 'Bearer',
    };
  }
  
  clear(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
  }
  
  isExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
  
  private getTokenExpiration(token: string): number {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000; // Convert to milliseconds
    } catch {
      return 0;
    }
  }
}

export const tokenStorage = new SecureTokenStorage();
```

### Automatic Token Refresh
```typescript
// Axios interceptor for automatic token refresh
export const setupAuthInterceptors = () => {
  // Request interceptor to add auth token
  axios.interceptors.request.use(
    (config) => {
      const token = tokenStorage.retrieve()?.accessToken;
      if (token && !tokenStorage.isExpired(token)) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor to handle token refresh
  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;
      
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          const tokens = tokenStorage.retrieve();
          if (tokens?.refreshToken) {
            const response = await authAPI.refreshToken(tokens.refreshToken);
            tokenStorage.store(response.data.tokens);
            
            // Retry original request with new token
            originalRequest.headers.Authorization = 
              `Bearer ${response.data.tokens.accessToken}`;
            return axios(originalRequest);
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          tokenStorage.clear();
          window.location.href = '/login';
        }
      }
      
      return Promise.reject(error);
    }
  );
};
```

## API Integration

### Authentication API
```typescript
interface AuthAPI {
  login: (credentials: LoginCredentials) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  refreshToken: (refreshToken: string) => Promise<RefreshResponse>;
  validateToken: (token: string) => Promise<User>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
}

class AuthenticationAPI implements AuthAPI {
  private readonly baseURL = process.env.NEXT_PUBLIC_API_URL;
  
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await axios.post(`${this.baseURL}/auth/login`, {
      username: credentials.username,
      password: credentials.password,
      rememberMe: credentials.rememberMe,
    });
    
    return response.data;
  }
  
  async logout(): Promise<void> {
    const token = tokenStorage.retrieve()?.accessToken;
    if (token) {
      await axios.post(`${this.baseURL}/auth/logout`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
    }
  }
  
  async refreshToken(refreshToken: string): Promise<RefreshResponse> {
    const response = await axios.post(`${this.baseURL}/auth/refresh`, {
      refreshToken,
    });
    
    return response.data;
  }
  
  async validateToken(token: string): Promise<User> {
    const response = await axios.get(`${this.baseURL}/auth/me`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    return response.data.user;
  }
  
  async forgotPassword(email: string): Promise<void> {
    await axios.post(`${this.baseURL}/auth/forgot-password`, { email });
  }
  
  async resetPassword(token: string, password: string): Promise<void> {
    await axios.post(`${this.baseURL}/auth/reset-password`, {
      token,
      password,
    });
  }
}

export const authAPI = new AuthenticationAPI();
```

## Security Features

### Security Event Logging
```typescript
interface SecurityEvent {
  type: 'LOGIN_SUCCESS' | 'LOGIN_FAILED' | 'LOGOUT' | 'TOKEN_REFRESH' | 'UNAUTHORIZED_ACCESS';
  userId?: string;
  username?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

const logSecurityEvent = (
  type: SecurityEvent['type'], 
  metadata: Record<string, any> = {}
) => {
  const event: SecurityEvent = {
    type,
    timestamp: new Date(),
    ipAddress: getClientIP(),
    userAgent: navigator.userAgent,
    ...metadata,
  };
  
  // Send to security logging service
  securityLogger.log(event);
};
```

### Rate Limiting
```typescript
class LoginRateLimiter {
  private attempts: Map<string, number> = new Map();
  private lockouts: Map<string, Date> = new Map();
  
  private readonly MAX_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  
  canAttemptLogin(identifier: string): boolean {
    const lockoutTime = this.lockouts.get(identifier);
    if (lockoutTime && Date.now() < lockoutTime.getTime()) {
      return false;
    }
    
    const attempts = this.attempts.get(identifier) || 0;
    return attempts < this.MAX_ATTEMPTS;
  }
  
  recordFailedAttempt(identifier: string): void {
    const attempts = (this.attempts.get(identifier) || 0) + 1;
    this.attempts.set(identifier, attempts);
    
    if (attempts >= this.MAX_ATTEMPTS) {
      const lockoutUntil = new Date(Date.now() + this.LOCKOUT_DURATION);
      this.lockouts.set(identifier, lockoutUntil);
    }
  }
  
  recordSuccessfulLogin(identifier: string): void {
    this.attempts.delete(identifier);
    this.lockouts.delete(identifier);
  }
  
  getRemainingLockoutTime(identifier: string): number {
    const lockoutTime = this.lockouts.get(identifier);
    if (!lockoutTime) return 0;
    
    const remaining = lockoutTime.getTime() - Date.now();
    return Math.max(0, remaining);
  }
}

export const rateLimiter = new LoginRateLimiter();
```

## Definition of Done
- [ ] Authentication context and provider implemented
- [ ] Secure token storage system created
- [ ] Automatic token refresh functionality working
- [ ] API integration completed and tested
- [ ] Security features implemented (rate limiting, logging)
- [ ] Error handling comprehensive and user-friendly
- [ ] Session management working correctly
- [ ] Logout functionality implemented
- [ ] Permission and role checking system working
- [ ] Security testing completed

## Priority: High
## Estimated Effort: 10 Story Points
## Sprint: Current + Next

## Dependencies
- Backend authentication API
- Login page design (Task 11)
- Protected routes system (Task 13)
- Security logging infrastructure

## Notes
- Consider implementing 2FA in future iterations
- Plan for SSO integration if needed
- Ensure compliance with security standards
- Consider implementing session timeout warnings
- Plan for password policy enforcement
