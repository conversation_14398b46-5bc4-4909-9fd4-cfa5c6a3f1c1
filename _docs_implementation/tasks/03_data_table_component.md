# Task 03: DataTable Component (Highly Reusable)

## User Story
**As a** user viewing data across different pages  
**I want** a consistent and feature-rich data table  
**So that** I can efficiently browse, search, filter, and interact with tabular data

## Acceptance Criteria

### Given: Data Table Requirements
- [ ] Table should support sorting, filtering, and searching
- [ ] Table should handle pagination efficiently
- [ ] Table should support row selection and bulk actions
- [ ] Table should support master-detail (expandable rows)
- [ ] Table should support data export in multiple formats

### When: Using Data Table
- [ ] Trip data displays with expandable details
- [ ] Port assignments show with action buttons
- [ ] Location data displays with status indicators
- [ ] Suspicious trips show with risk highlighting

### Then: Table Should
- [ ] Perform efficiently with large datasets (1000+ rows)
- [ ] Maintain state across page navigation
- [ ] Support keyboard navigation
- [ ] Work responsively on mobile devices
- [ ] Provide clear loading and error states

## Technical Requirements

### Core Interfaces
```typescript
interface TableColumn {
  key: string;
  titleKey: string; // Translation key instead of hardcoded text
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
  sticky?: boolean; // For horizontal scrolling
}

interface TableAction {
  key: string;
  labelKey: string; // Translation key for action label
  icon?: React.ReactNode;
  onClick: (row: any) => void;
  variant?: 'primary' | 'secondary' | 'danger';
  visible?: (row: any) => boolean;
  disabled?: (row: any) => boolean;
}

interface DataTableProps<T = any> {
  // Data
  data: T[];
  columns: TableColumn[];
  keyField?: string; // Unique identifier field (default: 'id')

  // Table Configuration
  pageSize?: number;
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  selectable?: boolean;

  // Master-Detail Configuration
  expandable?: boolean;
  expandedRowRender?: (row: T, index: number) => React.ReactNode;
  expandedRowKeys?: string[]; // Controlled expansion
  onExpandedRowsChange?: (expandedKeys: string[]) => void;
  expandRowByClick?: boolean; // Expand on row click vs expand button only
  expandIconColumnIndex?: number; // Which column to show expand icon (default: 0)
  expandIcon?: (expanded: boolean) => React.ReactNode;
  defaultExpandAllRows?: boolean;
  expandedRowClassName?: string;

  // Actions
  actions?: TableAction[];
  bulkActions?: TableAction[];
  onRowClick?: (row: T) => void;
  onSelectionChange?: (selectedRows: T[]) => void;

  // Export
  exportable?: boolean;
  exportFormats?: ('xlsx' | 'csv' | 'pdf')[];
  exportFileNameKey?: string; // Translation key for export filename
  onExport?: (format: string, data: T[]) => void;

  // Status Indicators
  rowStatusField?: string; // Field name for row status
  statusConfig?: {
    [key: string]: {
      color: string;
      icon?: React.ReactNode;
      highlight?: boolean;
    };
  };

  // Loading & Error States
  loading?: boolean;
  error?: string;
  emptyMessageKey?: string; // Translation key for empty state message

  // Styling
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  striped?: boolean;
  bordered?: boolean;
  stickyHeader?: boolean;
}
```

## Feature Requirements

### Sorting & Filtering
- [ ] Multi-column sorting with visual indicators
- [ ] Column-specific filter types (text, date, number, select)
- [ ] Global search across all searchable columns
- [ ] Filter persistence across page reloads

### Master-Detail (Expandable Rows)
- [ ] Smooth expand/collapse animations
- [ ] Nested content rendering with proper styling
- [ ] Keyboard navigation support
- [ ] Controlled and uncontrolled expansion modes

### Selection & Actions
- [ ] Single and multi-row selection
- [ ] Select all/none functionality
- [ ] Bulk action toolbar when rows selected
- [ ] Row-specific action buttons
- [ ] Action button visibility based on row data

### Export Functionality
- [ ] Excel (.xlsx) export with formatting
- [ ] CSV export for data analysis
- [ ] PDF export with table styling
- [ ] Custom export handlers

### Performance Optimization
- [ ] Virtual scrolling for large datasets
- [ ] Memoized row rendering
- [ ] Debounced search and filtering
- [ ] Lazy loading support

## Definition of Done
- [ ] DataTable component implemented with full TypeScript support
- [ ] All features working and tested
- [ ] Performance benchmarks met (render <200ms for 100 rows)
- [ ] Accessibility compliance (WCAG 2.1 AA)
- [ ] Mobile responsiveness implemented
- [ ] Comprehensive unit and integration tests
- [ ] Storybook documentation with examples
- [ ] Integration tested with real data sources

## Priority: High
## Estimated Effort: 13 Story Points
## Sprint: Current + Next

## Dependencies
- Export libraries (xlsx, jsPDF)
- Virtual scrolling library (optional)
- Translation system
- Icon library
- Base styling framework

## Notes
- Consider using react-table or similar library as foundation
- Plan for real-time data updates
- Ensure compatibility with existing data structures
- Consider server-side pagination for very large datasets
