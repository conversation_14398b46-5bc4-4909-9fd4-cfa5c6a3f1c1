# Task 13: Route Protection & Access Control

## User Story
**As a** system administrator  
**I want** to protect application routes based on authentication status  
**So that** only authenticated users can access protected pages and unauthorized users are redirected appropriately

## Acceptance Criteria

### Given: Route Protection Requirements
- [ ] System should protect all private routes from unauthorized access
- [ ] System should redirect unauthenticated users to login page
- [ ] System should handle role-based access control
- [ ] System should preserve intended destination after login

### When: User Attempts to Access Protected Routes
- [ ] Authenticated users can access authorized pages
- [ ] Unauthenticated users are redirected to login
- [ ] Users without proper permissions see appropriate error page
- [ ] Login redirects users to their intended destination

### Then: Route Protection Should
- [ ] Work seamlessly without user disruption
- [ ] Maintain security across all protected routes
- [ ] Provide clear feedback for access denials
- [ ] Handle edge cases gracefully
- [ ] Support nested route protection

## Route Protection Architecture

### Route Types Classification
```typescript
interface RouteConfig {
  path: string;
  component: React.ComponentType;
  protection: RouteProtection;
  permissions?: string[];
  roles?: string[];
  fallback?: string;
}

type RouteProtection = 
  | 'public'           // Accessible to everyone
  | 'auth-required'    // Requires authentication
  | 'guest-only'       // Only for non-authenticated users
  | 'role-based'       // Requires specific roles
  | 'permission-based' // Requires specific permissions
  | 'admin-only';      // Admin access only

const routeConfigs: RouteConfig[] = [
  // Public routes
  { path: '/login', component: LoginPage, protection: 'guest-only' },
  { path: '/forgot-password', component: ForgotPasswordPage, protection: 'guest-only' },
  { path: '/404', component: NotFoundPage, protection: 'public' },
  
  // Protected routes
  { path: '/dashboard', component: DashboardPage, protection: 'auth-required' },
  { path: '/trips', component: TripsPage, protection: 'auth-required' },
  { path: '/locations', component: LocationsPage, protection: 'auth-required' },
  
  // Role-based routes
  { 
    path: '/admin', 
    component: AdminPage, 
    protection: 'role-based',
    roles: ['admin', 'super-admin']
  },
  
  // Permission-based routes
  { 
    path: '/reports/financial', 
    component: FinancialReportsPage, 
    protection: 'permission-based',
    permissions: ['view-financial-reports']
  },
];
```

### Protected Route Component
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  protection: RouteProtection;
  permissions?: string[];
  roles?: string[];
  fallback?: string;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  protection,
  permissions = [],
  roles = [],
  fallback = '/unauthorized',
  redirectTo = '/login',
}) => {
  const { isAuthenticated, isLoading, user, hasPermission, hasRole } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      // Wait for auth to initialize
      if (isLoading) return;
      
      const currentPath = router.asPath;
      
      switch (protection) {
        case 'public':
          setIsChecking(false);
          break;
          
        case 'guest-only':
          if (isAuthenticated) {
            router.replace('/dashboard');
          } else {
            setIsChecking(false);
          }
          break;
          
        case 'auth-required':
          if (!isAuthenticated) {
            // Store intended destination
            sessionStorage.setItem('redirectAfterLogin', currentPath);
            router.replace(redirectTo);
          } else {
            setIsChecking(false);
          }
          break;
          
        case 'role-based':
          if (!isAuthenticated) {
            sessionStorage.setItem('redirectAfterLogin', currentPath);
            router.replace(redirectTo);
          } else if (!roles.some(role => hasRole(role))) {
            router.replace(fallback);
          } else {
            setIsChecking(false);
          }
          break;
          
        case 'permission-based':
          if (!isAuthenticated) {
            sessionStorage.setItem('redirectAfterLogin', currentPath);
            router.replace(redirectTo);
          } else if (!permissions.some(permission => hasPermission(permission))) {
            router.replace(fallback);
          } else {
            setIsChecking(false);
          }
          break;
          
        case 'admin-only':
          if (!isAuthenticated) {
            sessionStorage.setItem('redirectAfterLogin', currentPath);
            router.replace(redirectTo);
          } else if (!hasRole('admin') && !hasRole('super-admin')) {
            router.replace(fallback);
          } else {
            setIsChecking(false);
          }
          break;
          
        default:
          setIsChecking(false);
      }
    };

    checkAccess();
  }, [isAuthenticated, isLoading, user, router, protection, permissions, roles]);

  // Show loading while checking access
  if (isLoading || isChecking) {
    return <RouteLoadingSpinner />;
  }

  return <>{children}</>;
};
```

### Route Guard Hook
```typescript
interface UseRouteGuardOptions {
  requiredPermissions?: string[];
  requiredRoles?: string[];
  redirectTo?: string;
  onAccessDenied?: () => void;
}

export const useRouteGuard = (options: UseRouteGuardOptions = {}) => {
  const { 
    requiredPermissions = [], 
    requiredRoles = [],
    redirectTo = '/unauthorized',
    onAccessDenied 
  } = options;
  
  const { isAuthenticated, hasPermission, hasRole } = useAuth();
  const router = useRouter();

  const checkAccess = useCallback(() => {
    if (!isAuthenticated) {
      return false;
    }

    // Check required permissions
    if (requiredPermissions.length > 0) {
      const hasRequiredPermissions = requiredPermissions.every(permission => 
        hasPermission(permission)
      );
      if (!hasRequiredPermissions) {
        return false;
      }
    }

    // Check required roles
    if (requiredRoles.length > 0) {
      const hasRequiredRoles = requiredRoles.some(role => hasRole(role));
      if (!hasRequiredRoles) {
        return false;
      }
    }

    return true;
  }, [isAuthenticated, requiredPermissions, requiredRoles, hasPermission, hasRole]);

  const enforceAccess = useCallback(() => {
    if (!checkAccess()) {
      if (onAccessDenied) {
        onAccessDenied();
      } else {
        router.replace(redirectTo);
      }
    }
  }, [checkAccess, onAccessDenied, router, redirectTo]);

  return {
    hasAccess: checkAccess(),
    enforceAccess,
    checkAccess,
  };
};
```

## Next.js Integration

### App Router Implementation (Next.js 13+)
```typescript
// app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html>
      <body>
        <AuthProvider>
          <RouteProtectionProvider>
            {children}
          </RouteProtectionProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

// app/(protected)/dashboard/page.tsx
export default function DashboardPage() {
  return (
    <ProtectedRoute protection="auth-required">
      <Dashboard />
    </ProtectedRoute>
  );
}

// app/(protected)/admin/page.tsx
export default function AdminPage() {
  return (
    <ProtectedRoute 
      protection="role-based" 
      roles={['admin', 'super-admin']}
    >
      <AdminDashboard />
    </ProtectedRoute>
  );
}
```

### Middleware for Route Protection
```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from './lib/auth';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Define protected routes
  const protectedRoutes = [
    '/dashboard',
    '/trips',
    '/locations',
    '/vehicles',
    '/drivers',
    '/ports',
    '/reports',
    '/admin',
  ];
  
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  if (isProtectedRoute) {
    const token = request.cookies.get('auth-token')?.value;
    
    if (!token) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
    
    try {
      const payload = await verifyToken(token);
      
      // Check role-based access for admin routes
      if (pathname.startsWith('/admin')) {
        const userRoles = payload.roles || [];
        if (!userRoles.includes('admin') && !userRoles.includes('super-admin')) {
          return NextResponse.redirect(new URL('/unauthorized', request.url));
        }
      }
      
      // Add user info to headers for server components
      const response = NextResponse.next();
      response.headers.set('x-user-id', payload.userId);
      response.headers.set('x-user-roles', JSON.stringify(payload.roles));
      return response;
      
    } catch (error) {
      // Invalid token, redirect to login
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|login|forgot-password).*)',
  ],
};
```

## Access Control Components

### Permission-Based Rendering
```typescript
interface PermissionGateProps {
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean; // true = AND logic, false = OR logic
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = null,
  children,
}) => {
  const { hasPermission, hasRole } = useAuth();

  const checkPermissions = () => {
    if (permissions.length === 0 && roles.length === 0) {
      return true;
    }

    const permissionCheck = permissions.length === 0 ? true : 
      requireAll 
        ? permissions.every(p => hasPermission(p))
        : permissions.some(p => hasPermission(p));

    const roleCheck = roles.length === 0 ? true :
      requireAll
        ? roles.every(r => hasRole(r))
        : roles.some(r => hasRole(r));

    return requireAll ? (permissionCheck && roleCheck) : (permissionCheck || roleCheck);
  };

  if (!checkPermissions()) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Usage examples
const AdminPanel = () => (
  <PermissionGate roles={['admin']}>
    <AdminDashboard />
  </PermissionGate>
);

const FinancialReports = () => (
  <PermissionGate 
    permissions={['view-financial-reports']}
    fallback={<div>Access denied to financial reports</div>}
  >
    <FinancialReportsList />
  </PermissionGate>
);
```

### Navigation Guard
```typescript
interface NavigationGuardProps {
  to: string;
  permissions?: string[];
  roles?: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const NavigationGuard: React.FC<NavigationGuardProps> = ({
  to,
  permissions = [],
  roles = [],
  children,
  fallback,
}) => {
  const { hasAccess } = useRouteGuard({
    requiredPermissions: permissions,
    requiredRoles: roles,
  });

  if (!hasAccess) {
    return fallback ? <>{fallback}</> : null;
  }

  return (
    <Link href={to}>
      {children}
    </Link>
  );
};
```

## Error Pages

### Unauthorized Access Page
```typescript
const UnauthorizedPage: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();

  return (
    <div className="unauthorized-page">
      <div className="error-content">
        <div className="error-icon">
          <ShieldXIcon size={64} />
        </div>
        
        <Typography variant="h1" className="error-title">
          Access Denied
        </Typography>
        
        <Typography variant="body1" className="error-description">
          You don't have permission to access this page.
          {user && (
            <span>
              {' '}Contact your administrator if you believe this is an error.
            </span>
          )}
        </Typography>
        
        <div className="error-actions">
          <Button
            variant="primary"
            onClick={() => router.back()}
          >
            Go Back
          </Button>
          
          <Button
            variant="secondary"
            onClick={() => router.push('/dashboard')}
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};
```

## Redirect After Login

### Login Redirect Handler
```typescript
export const useLoginRedirect = () => {
  const router = useRouter();
  
  const handleSuccessfulLogin = useCallback(() => {
    // Get stored redirect path
    const redirectPath = sessionStorage.getItem('redirectAfterLogin');
    
    if (redirectPath) {
      sessionStorage.removeItem('redirectAfterLogin');
      router.replace(redirectPath);
    } else {
      // Default redirect to dashboard
      router.replace('/dashboard');
    }
  }, [router]);
  
  return { handleSuccessfulLogin };
};

// Usage in login component
const LoginPage = () => {
  const { login } = useAuth();
  const { handleSuccessfulLogin } = useLoginRedirect();
  
  const handleLogin = async (credentials: LoginCredentials) => {
    try {
      await login(credentials);
      handleSuccessfulLogin();
    } catch (error) {
      // Handle login error
    }
  };
  
  // ... rest of component
};
```

## Testing Strategy

### Route Protection Tests
```typescript
describe('Route Protection', () => {
  it('should redirect unauthenticated users to login', async () => {
    render(<ProtectedRoute protection="auth-required">Protected Content</ProtectedRoute>);
    
    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith('/login');
    });
  });
  
  it('should allow access for authenticated users', async () => {
    mockAuth.isAuthenticated = true;
    
    render(<ProtectedRoute protection="auth-required">Protected Content</ProtectedRoute>);
    
    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });
  
  it('should check role-based access', async () => {
    mockAuth.isAuthenticated = true;
    mockAuth.hasRole = jest.fn().mockReturnValue(false);
    
    render(
      <ProtectedRoute protection="role-based" roles={['admin']}>
        Admin Content
      </ProtectedRoute>
    );
    
    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith('/unauthorized');
    });
  });
});
```

## Definition of Done
- [ ] ProtectedRoute component implemented and tested
- [ ] Route guard hooks created and working
- [ ] Middleware for server-side protection implemented
- [ ] Permission-based rendering components created
- [ ] Error pages for unauthorized access implemented
- [ ] Login redirect functionality working
- [ ] Role and permission checking system integrated
- [ ] Navigation guards implemented
- [ ] Comprehensive testing completed
- [ ] Documentation for route protection created

## Priority: High
## Estimated Effort: 8 Story Points
## Sprint: Current

## Dependencies
- Authentication system (Task 12)
- User roles and permissions system
- Error page components
- Navigation system

## Notes
- Consider implementing route-level loading states
- Plan for dynamic permission updates
- Ensure SEO considerations for protected routes
- Consider implementing breadcrumb-based access control
- Plan for audit logging of access attempts
