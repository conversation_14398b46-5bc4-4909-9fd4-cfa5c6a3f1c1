# Authentication System Documentation

## Overview

This document provides a complete guide to the authentication system in the Trip Tracking System. The system is built with React/Next.js and provides secure user authentication with session management.

## Test Accounts

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Admin | `admin` | `admin123` | Full system access |
| Operator | `operator` | `operator123` | Limited permissions |
| Supervisor | `supervisor` | `supervisor123` | Moderate permissions |

## System Architecture

### Core Files Structure

```
utils/
├── mockUsers.ts        # User data and validation functions
├── auth.ts             # Authentication core functions

types/
├── auth.ts             # TypeScript type definitions

constants/
├── auth.ts             # System constants

contexts/
├── AuthContext.tsx     # React context for auth state

components/auth/
├── ProtectedRoute.tsx  # Route protection component
```

### File Responsibilities

#### `utils/mockUsers.ts` - User Data Management
Contains user data and utility functions for user operations.

```typescript
// User data array
export const MOCK_USERS: User[] = [
  { id: "1", username: "admin", password: "admin123", ... },
  { id: "2", username: "operator", password: "operator123", ... },
  { id: "3", username: "supervisor", password: "supervisor123", ... }
];

// Utility functions
export const validateUserCredentials = (username, password) => { /* validation logic */ };
export const findUserByUsername = (username) => { /* search logic */ };
export const getActiveUsers = () => { /* filter active users */ };
```

#### `utils/auth.ts` - Authentication Functions
Core authentication logic and session management.

```typescript
// Login function
export const authenticateUser = async (credentials) => { /* authentication logic */ };

// Session management
export const storeUserSession = (user, rememberMe) => { /* store session */ };
export const getCurrentUser = () => { /* get current user */ };
export const clearUserSession = () => { /* clear session */ };

// Authentication checks
export const isAuthenticated = () => { /* check if logged in */ };
export const logout = () => { /* logout and redirect */ };
```

#### `types/auth.ts` - Type Definitions
TypeScript interfaces for type safety.

```typescript
export interface User {
  id: string;
  username: string;
  employeeName: string;
  role: UserRole;
  permissions: UserPermissions;
  // ... other properties
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  message?: string;
}
```

#### `constants/auth.ts` - System Constants
Configuration values and constants.

```typescript
export const STORAGE_KEYS = {
  USER_SESSION: 'zatca_user_session'
};

export const SESSION_DURATION = {
  REGULAR: 8 * 60 * 60 * 1000,    // 8 hours
  EXTENDED: 24 * 60 * 60 * 1000   // 24 hours
};

export const AUTH_ERROR_CODES = {
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_INACTIVE: 'ACCOUNT_INACTIVE'
};
```

#### `contexts/AuthContext.tsx` - State Management
React context for managing authentication state across the application.

```typescript
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const login = (userData) => setUser(userData);
  const logout = () => { /* logout logic */ };
  
  return (
    <AuthContext.Provider value={{ user, isAuthenticated: !!user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
```

#### `components/auth/ProtectedRoute.tsx` - Route Protection
Component that protects routes from unauthorized access.

```typescript
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) return <LoadingSpinner />;
  return isAuthenticated ? <>{children}</> : null;
};
```

## Data Flow

### Login Process
```
1. User enters credentials → app/login/page.tsx
2. Call authenticateUser() → utils/auth.ts
3. Validate credentials → utils/mockUsers.ts
4. Store session → utils/auth.ts (storeUserSession)
5. Update context → contexts/AuthContext.tsx (login)
6. Redirect to main page
```

### Page Protection
```
1. User accesses protected page
2. ProtectedRoute component checks authentication
3. useAuth() hook gets current state
4. getCurrentUser() checks stored session
5. Either allow access or redirect to login
```

### Logout Process
```
1. User clicks logout → components/layout/Header.tsx
2. Call logout() → contexts/AuthContext.tsx
3. Clear session → utils/auth.ts (clearUserSession)
4. Redirect to login page
```

## Usage Examples

### Basic Login Implementation
```typescript
import { authenticateUser, storeUserSession } from '@/utils/auth';
import { useAuth } from '@/contexts/AuthContext';

const LoginPage = () => {
  const { login } = useAuth();
  
  const handleLogin = async (credentials) => {
    const result = await authenticateUser(credentials);
    if (result.success) {
      storeUserSession(result.user, credentials.rememberMe);
      login(result.user);
      router.push('/dashboard');
    }
  };
};
```

### Protecting a Page
```typescript
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <div>Protected content here</div>
    </ProtectedRoute>
  );
}
```

### Using Authentication State
```typescript
import { useAuth } from '@/contexts/AuthContext';

const MyComponent = () => {
  const { user, isAuthenticated, logout } = useAuth();
  
  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }
  
  return (
    <div>
      <p>Welcome, {user.employeeName}</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
};
```

## Adding New Users

To add a new user, edit the `MOCK_USERS` array in `utils/mockUsers.ts`:

```typescript
export const MOCK_USERS: User[] = [
  // Existing users...
  {
    id: "4",
    username: "newuser",
    password: "password123",
    employeeName: "New User",
    employeeNameArabic: "مستخدم جديد",
    email: "<EMAIL>",
    role: "viewer",
    organization: "zatca",
    status: "active",
    permissions: {
      canViewTrips: true,
      canManageTrips: false,
      canViewAlerts: true,
      canManageAlerts: false,
      canManageUsers: false,
      canAssignPorts: false,
      canViewReports: false,
      canExportData: false
    },
    profile: {
      department: "New Department",
      phoneNumber: "+966501234567",
      preferredLanguage: "ar",
      timezone: "Asia/Riyadh"
    }
  }
];
```

## Available Functions

### Authentication Functions (`utils/auth.ts`)
- `authenticateUser(credentials)` - Authenticate user login
- `storeUserSession(user, rememberMe)` - Store user session
- `getCurrentUser()` - Get current logged-in user
- `clearUserSession()` - Clear user session
- `isAuthenticated()` - Check if user is authenticated
- `logout()` - Logout user and redirect

### User Management Functions (`utils/mockUsers.ts`)
- `validateUserCredentials(username, password)` - Validate login credentials
- `findUserByUsername(username)` - Find user by username
- `findUserById(id)` - Find user by ID
- `getActiveUsers()` - Get all active users

### Context Hooks (`contexts/AuthContext.tsx`)
- `useAuth()` - Get authentication state and functions
  - `user` - Current user data
  - `isAuthenticated` - Authentication status
  - `isLoading` - Loading state
  - `login(user)` - Login function
  - `logout()` - Logout function

## Security Features

### Current Implementation
- Browser environment checks to prevent SSR errors
- Automatic session expiration (8 hours regular, 24 hours with "remember me")
- Route protection for unauthorized access
- Standardized error codes

### Production Considerations
- Hash passwords with bcrypt
- Use JWT tokens with proper signing
- Implement HTTPS everywhere
- Add rate limiting for login attempts
- Use secure, httpOnly cookies
- Implement CSRF protection
- Add audit logging

## File Integration

The files work together in this hierarchy:

```
app/login/page.tsx
    ↓ imports
utils/auth.ts
    ↓ imports
utils/mockUsers.ts (data) + constants/auth.ts (config) + types/auth.ts (types)
    ↓ used by
contexts/AuthContext.tsx
    ↓ used by
components/auth/ProtectedRoute.tsx
    ↓ protects
All protected pages
```

This creates a clean separation of concerns where each file has a specific responsibility while working together to provide a complete authentication system.
