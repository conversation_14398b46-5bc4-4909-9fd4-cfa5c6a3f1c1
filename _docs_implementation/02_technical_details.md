# Technical Implementation Details

## Architecture Overview

The TTS Template implements a modular architecture with clear separation of concerns:

- **UI Layer**: React components with shadcn/ui integration
- **State Management**: React Context for language and component state
- **API Layer**: HTTP-like functions with static JSON data sources
- **Translation System**: Modular translation files with TypeScript support
- **Styling**: Tailwind CSS 4+ with custom RTL utilities

## API Integration Pattern

### Static JSON Data Layer
The application uses a static JSON approach that mimics real API behavior:

```typescript
// api/shipment-alerts.ts
export async function getShipmentAlerts(filters?: SearchFilters): Promise<AlertsResponse> {
  // Simulates API call with static data
  // Easy migration path to real backend APIs
}
```

### Data Flow Architecture
1. **Components** call API functions
2. **API Layer** processes requests and applies filters
3. **Static JSON** provides data source
4. **TypeScript Interfaces** ensure type safety
5. **React State** manages UI updates

### Migration Strategy
The API layer is designed for easy migration to real backends:
- Replace static imports with HTTP calls
- Maintain same function signatures
- Keep TypeScript interfaces unchanged
- Components require no modifications

## Dashboard Architecture

### Component Hierarchy
```
ShipmentDashboard
├── SummaryCard (×5)
├── SearchPanel
├── ViewToggle
└── AlertsList | AlertsTable
```

### State Management
- **View State**: Table vs List view preference
- **Filter State**: Search and filter parameters
- **Data State**: Alerts data and loading states
- **Error State**: Error handling and display

### Data Visualization Components
- **SummaryCard**: Reusable metric cards with icons
- **AlertsList**: Card-based detailed view
- **AlertsTable**: Tabular data with sorting
- **SearchPanel**: Advanced filtering interface

## Language Management System

### Context-Based Internationalization
The application uses a custom React Context (`LanguageContext`) for managing language state and translations:

```typescript
// contexts/LanguageContext.tsx
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  dir: 'ltr' | 'rtl';
}
```

### Key Features:
1. **Persistent Storage**: Language preference saved to localStorage
2. **Dynamic Direction**: Automatically updates `document.documentElement.dir`
3. **Translation Function**: Simple key-based translation system
4. **Type Safety**: TypeScript interfaces for all language operations

## Styling Implementation

### Tailwind CSS 4+ Configuration
```css
/* app/globals.css */
@theme inline {
  /* Custom color variables */
  --color-primary-blue: #082d4f;
  --color-secondary-blue: #007bff;
  --color-teal: #30B4B4;
  
  /* Header dimensions */
  --header-top-height: 3rem;
  --header-nav-height: 3.5rem;
}
```

### RTL Support Classes
```css
/* Custom RTL utilities */
.rtl\:flex-row-reverse[dir="rtl"] {
  flex-direction: row-reverse;
}

.rtl\:text-right[dir="rtl"] {
  text-align: right;
}
```

## Component Architecture

### Header Component Structure
```
Header
├── Top Bar (h-12, bg-primary-blue)
│   ├── Left: LanguageSwitcher
│   └── Right: UserDropdown + AgencyBranding
└── Navigation Bar (h-14, bg-white)
    └── Navigation Items with Dropdowns
```

### Language Switcher Implementation
- Globe icon from Lucide React
- Context-based language toggling
- Immediate UI updates
- localStorage persistence

### Footer Component
- Centered bilingual branding
- Dynamic text based on current language
- Responsive layout

## State Management

### Language State Flow
1. **Initialization**: Load from localStorage or default to 'en'
2. **User Action**: Click language switcher
3. **State Update**: Update context state
4. **Persistence**: Save to localStorage
5. **DOM Update**: Update document direction and language
6. **Re-render**: Components re-render with new translations

### Translation System
The application uses a modular translation system with separate files:

```typescript
// translations/en.ts
export const en = {
  'header.userAccount': 'Ahmed Ali',
  'navigation.dashboard': 'Dashboard'
} as const;

// translations/ar.ts
export const ar = {
  'header.userAccount': 'أحمد علي',
  'navigation.dashboard': 'لوحة التحكم'
} as const;
```

For detailed translation implementation, see `03_translation_implementation.md`

## Responsive Design

### Breakpoints Used
- `sm:` - 640px and up
- `md:` - 768px and up
- `lg:` - 1024px and up

### Mobile Considerations
- Horizontal scrolling for navigation on small screens
- Responsive spacing and typography
- Touch-friendly button sizes
- Collapsible elements where needed

## Performance Optimizations

### Bundle Size
- Tree-shaking enabled
- Only necessary shadcn/ui components imported
- Minimal external dependencies

### Runtime Performance
- Client-side language switching (no page reloads)
- localStorage caching
- Efficient re-renders with React Context

## Browser Compatibility
- Modern browsers supporting ES2017+
- CSS Grid and Flexbox support required
- localStorage API required for language persistence

## Mobile Navigation System

### Responsive Design Implementation
The navigation system adapts to different screen sizes:

**Desktop (md and above):**
- Full horizontal navigation bar
- All menu items visible
- Dropdown menus for Reports section

**Mobile (below md breakpoint):**
- Hamburger menu button
- Slide-out navigation panel
- Full-screen overlay with backdrop
- Touch-friendly interface

### Mobile Navigation Components

#### MobileNavigation Component
```typescript
// Slide-out panel with proper RTL support
const panelPosition = dir === 'rtl' ? 'left-0' : 'right-0';
```

#### Breadcrumb Component
```typescript
// Dynamic breadcrumb generation based on current route
const generateBreadcrumbs = (): BreadcrumbItem[] => {
  // Maps pathname to translation keys
  // Supports RTL chevron direction
};
```

### RTL Mobile Navigation
- **Panel Position**: Slides from left in RTL, right in LTR
- **Menu Sequence**: Maintains same order in both directions
- **Animations**: Direction-aware slide animations
- **Icons**: Chevrons rotate appropriately for RTL

### Mobile-Specific Features
1. **Touch Optimization**: Larger touch targets (py-3)
2. **Backdrop Dismiss**: Tap outside to close
3. **Responsive Width**: Max 85% viewport width
4. **Smooth Animations**: CSS transitions for open/close
5. **Accessibility**: Proper ARIA labels and keyboard support

## Development Workflow
1. **Development Server**: `npm run dev`
2. **Build**: `npm run build`
3. **Type Checking**: Built-in TypeScript checking
4. **Linting**: ESLint configuration included
5. **Mobile Testing**: Use browser dev tools to test responsive design
