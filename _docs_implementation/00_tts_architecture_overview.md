# Trip Tracking System (TTS) - Technical Architecture Documentation

## System Overview

The Trip Tracking System (TTS) is a comprehensive logistics monitoring platform designed specifically for the **Zakat, Tax and Customs Authority (ZATCA)** to provide real-time tracking and management of cargo transportation across Saudi Arabia's border control points, airports, seaports, and land ports.

### Primary Purpose

The system serves as a centralized monitoring dashboard for ZATCA employees to:

- **Monitor cross-border cargo movements** in real-time
- **Receive and manage critical alerts** from tracking devices
- **Ensure compliance** with customs regulations and security protocols
- **Track shipment progress** through designated routes and checkpoints
- **Coordinate responses** to security breaches and operational incidents

### Target Users

- **ZATCA Customs Officers**: Monitor assigned ports and routes
- **ZATCA Supervisors**: Oversee multiple locations and coordinate responses
- **ZATCA Administrators**: Manage system users and configure monitoring parameters
- **System Administrators**: Maintain technical infrastructure and integrations

### Core Dashboard Modules

1. **Location Monitor** (`/location-monitor`) - Primary real-time monitoring dashboard for tracking active trips across assigned locations
2. **Focused Trips** (`/focused-trips`) - Dedicated view for high-priority or flagged shipments requiring special attention
3. **My Assigned Ports** (`/my-assigned-ports`) - Personal workspace showing ports and routes assigned to the current ZATCA officer (user))
4. **Dashboard** (`/dashboard`) - Comprehensive overview with statistics, alerts summary, and system status
5. **Configuration** (`/configuration`) - System settings, user preferences, and monitoring parameters
6. **Suspicious Trips** (`/suspicious-trips`) - Security-focused view for trips flagged by automated risk assessment
7. **Reports** (`/reports`) - Comprehensive reporting module with:
   - Operational Reports (trip statistics, performance metrics)
   - Compliance Reports (regulatory adherence, audit trails)
   - Security Reports (incident analysis, threat assessments)

### Core Functionality

The system provides end-to-end visibility of shipment movements with integrated alert management, compliance monitoring, and real-time dashboard capabilities. ZATCA employees can observe trips within their assigned port jurisdictions and receive immediate notifications of any security or operational concerns through the specialized dashboard modules.

## System Architecture Diagram

```mermaid
graph TD
    subgraph "ZATCA Dashboard"
        A[Location Monitor]
        B[Focused Trips]
        C[My Assigned Ports]
        D[Dashboard Overview]
        E[Suspicious Trips]
        F[Reports]
    end

    subgraph "Backend Services"
        G[Trip Monitoring]
        H[Alert Processing]
        I[User Management]
    end

    subgraph "Data Storage"
        J[(Database)]
    end

    subgraph "External Systems"
        K[IoT Devices]
        L[Transportation Companies]
    end

    A --> G
    B --> G
    C --> I
    D --> G
    E --> H
    F --> G

    G --> J
    H --> J
    I --> J

    K --> G
    L --> G
```

## Technical Overview

### Core Technologies

The Trip Tracking System (TTS) is built using modern web technologies optimized for performance, scalability, and maintainability in government environments.

#### **Frontend Framework**

- **Next.js 15.3.5** - React-based framework with server-side rendering capabilities
- **React 19+** - Component-based UI library for building interactive interfaces
- **TypeScript** - Strongly typed JavaScript for enhanced code quality and developer experience
- **Turbopack** - Next-generation bundler for faster development builds

#### **Styling and UI Framework**

- **Tailwind CSS** - Utility-first CSS framework for rapid UI development
- **Shadcn/UI** - High-quality, accessible component library built on Radix UI
- **Lucide React** - Beautiful, customizable SVG icons

#### **State Management and Data Fetching**

- **React Hooks** - Built-in state management for component-level state
- **Context API** - Global state management for user preferences and language
- **Static JSON APIs** - Mock data layer for development and demonstration
- **Async/Await Patterns** - Modern JavaScript for handling asynchronous operations
- 

#### **Development and Build Tools**

- **ESLint** - Code linting and quality enforcement
- **Prettier** - Code formatting and style consistency
- **npm/yarn** - Package management and dependency resolution
- **Git** - Version control and collaboration

### Key Dependencies

#### **Production Dependencies**

```json
{
  "next": "15.3.5",
  "react": "^18.0.0",
  "react-dom": "^18.0.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^3.4.0",
  "lucide-react": "^0.400.0",
  "@radix-ui/react-*": "^1.0.0",
  "class-variance-authority": "^0.7.0",
  "clsx": "^2.0.0",
  "tailwind-merge": "^2.0.0"
}
```

#### **Development Dependencies**

```json
{
  "@types/node": "^20.0.0",
  "@types/react": "^18.0.0",
  "@types/react-dom": "^18.0.0",
  "eslint": "^8.0.0",
  "eslint-config-next": "15.3.5",
  "postcss": "^8.0.0",
  "autoprefixer": "^10.0.0"
}
```

#### **Optional Enhancement Dependencies**

```json
{
  "recharts": "^2.8.0",
  "@googlemaps/js-api-loader": "^1.16.0",
  "framer-motion": "^10.0.0",
  "react-hook-form": "^7.0.0",
  "zod": "^3.0.0"
}
```

### Styling Strategy

#### **Design System Architecture**

The TTS employs a comprehensive design system built on Tailwind CSS and Shadcn/UI components to ensure consistency across all ZATCA dashboard modules.

#### **Utility-First Approach**

- **Tailwind CSS Classes** - Atomic CSS utilities for rapid development
- **Component Variants** - Systematic approach to component styling variations
- **Responsive Design** - Mobile-first approach with breakpoint-specific utilities
- **Dark/Light Mode Support** - CSS custom properties for theme switching

#### **Component Styling Patterns**

```typescript
// Example: Button component with variants
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline"
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10"
      }
    }
  }
)
```

#### **Color System**

- **Primary Colors** - ZATCA brand colors for primary actions and navigation
- **Semantic Colors** - Status indicators (success, warning, error, info)
- **Neutral Palette** - Grayscale for text, borders, and backgrounds
- **Custom Properties** - CSS variables for dynamic theming

#### **Typography Scale**

- **Font Family** - System fonts optimized for Arabic and English text
- **Font Sizes** - Consistent scale from text-xs to text-6xl
- **Font Weights** - Regular, medium, semibold, and bold weights
- **Line Heights** - Optimized for readability in both languages

### Internationalization Approach

#### **Bilingual Support Architecture**

The system provides comprehensive Arabic and English language support without URL prefixes, using a context-based approach for seamless language switching.

#### **Translation Management**

```typescript
// Translation structure
interface Translations {
  'header.language': string;
  'header.agencyName': string;
  'navigation.locationMonitor': string;
  'demoTrip.title': string;
  // ... additional keys
}

// Usage pattern
const { t, language, setLanguage, dir } = useLanguage();
```

#### **Language Context Implementation**

- **React Context** - Global language state management
- **Dynamic Loading** - Translation files loaded based on user preference
- **Fallback System** - English fallback for missing Arabic translations
- **Persistent Storage** - User language preference saved in localStorage

#### **RTL/LTR Layout Support**

- **Direction Context** - Automatic layout direction based on language
- **CSS Logical Properties** - margin-inline, padding-inline for direction-agnostic styling
- **Icon Mirroring** - Directional icons automatically flipped for RTL
- **Text Alignment** - Dynamic text alignment based on reading direction

#### **Translation File Structure**

```typescript
// translations/en.ts
export const en = {
  'header.language': 'English',
  'header.agencyName': 'Zakat, Tax and Customs Authority',
  'navigation.locationMonitor': 'Location Monitor',
  'demoTrip.title': 'Demo Trip Management'
} as const;

// translations/ar.ts
export const ar = {
  'header.language': 'العربية',
  'header.agencyName': 'هيئة الزكاة والضريبة والجمارك',
  'navigation.locationMonitor': 'مراقب الموقع',
  'demoTrip.title': 'إدارة الرحلات التجريبية'
} as const;
```

### Component Structure

#### **Architectural Patterns**

The TTS follows a modular component architecture with clear separation of concerns and reusable design patterns.

#### **Component Hierarchy**

```
src/
├── app/                          # Next.js App Router pages
│   ├── layout.tsx               # Root layout with providers
│   ├── page.tsx                 # Home page
│   ├── demo/                    # Demo pages
│   ├── demo-trip/               # Trip management pages
│   ├── location-monitor/        # Location monitoring
│   └── [other-modules]/         # Additional dashboard modules
├── components/
│   ├── ui/                      # Base UI components (Shadcn/UI)
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── table.tsx
│   │   └── [other-primitives]/
│   ├── layout/                  # Layout components
│   │   ├── Header.tsx
│   │   ├── Navbar.tsx
│   │   ├── Footer.tsx
│   │   └── PageTemplate.tsx
│   ├── features/                # Feature-specific components
│   │   ├── AlertsTable.tsx
│   │   ├── TripCard.tsx
│   │   └── MapComponent.tsx
│   └── shared/                  # Shared utility components
├── contexts/                    # React contexts
│   └── LanguageContext.tsx
├── api/                        # API layer and data management
│   ├── demo_trips.ts
│   ├── alerts.ts
│   └── [other-apis]/
├── data/                       # Static data files
│   ├── demo_trips.json
│   └── [other-data]/
└── translations/               # Internationalization
    ├── en.ts
    └── ar.ts
```

#### **Component Design Patterns**

##### **1. Compound Components**

```typescript
// Example: Card compound component
export const Card = ({ children, className, ...props }) => (
  <div className={cn("rounded-lg border bg-card", className)} {...props}>
    {children}
  </div>
);

Card.Header = ({ children, className, ...props }) => (
  <div className={cn("flex flex-col space-y-1.5 p-6", className)} {...props}>
    {children}
  </div>
);

Card.Content = ({ children, className, ...props }) => (
  <div className={cn("p-6 pt-0", className)} {...props}>
    {children}
  </div>
);
```

##### **2. Render Props Pattern**

```typescript
// Example: Data fetching component
interface DataFetcherProps<T> {
  fetchData: () => Promise<T>;
  children: (data: T | null, loading: boolean, error: string | null) => React.ReactNode;
}

export function DataFetcher<T>({ fetchData, children }: DataFetcherProps<T>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ... fetch logic

  return children(data, loading, error);
}
```

##### **3. Higher-Order Components (HOCs)**

```typescript
// Example: withAuth HOC for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth();

    if (loading) return <LoadingSpinner />;
    if (!user) return <LoginForm />;

    return <Component {...props} />;
  };
}
```

#### **State Management Patterns**

##### **1. Local Component State**

```typescript
// Simple component state for UI interactions
const [isOpen, setIsOpen] = useState(false);
const [selectedItem, setSelectedItem] = useState<string | null>(null);
```

##### **2. Context-Based Global State**

```typescript
// Language context for global language state
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  // ... context logic
  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, dir }}>
      {children}
    </LanguageContext.Provider>
  );
}
```

##### **3. Custom Hooks for Logic Reuse**

```typescript
// Custom hook for data fetching
export function useTrips() {
  const [trips, setTrips] = useState<DemoTrip[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTrips = async () => {
      try {
        const response = await getDemoTrips();
        setTrips(response.trips);
      } catch (error) {
        console.error('Error fetching trips:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrips();
  }, []);

  return { trips, loading };
}
```

#### **Component Communication Patterns**

##### **1. Props Down, Events Up**

```typescript
// Parent component
function TripManagement() {
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null);

  return (
    <div>
      <TripList onTripSelect={setSelectedTrip} />
      {selectedTrip && <TripDetails tripId={selectedTrip} />}
    </div>
  );
}

// Child component
function TripList({ onTripSelect }: { onTripSelect: (id: string) => void }) {
  return (
    <div>
      {trips.map(trip => (
        <TripCard key={trip.id} trip={trip} onClick={() => onTripSelect(trip.id)} />
      ))}
    </div>
  );
}
```

##### **2. Context for Cross-Component Communication**

```typescript
// Shared state across distant components
const AlertContext = createContext<AlertContextType>();

function useAlerts() {
  const context = useContext(AlertContext);
  if (!context) throw new Error('useAlerts must be used within AlertProvider');
  return context;
}
```

#### **Performance Optimization Patterns**

##### **1. React.memo for Component Memoization**

```typescript
export const TripCard = React.memo(({ trip }: { trip: DemoTrip }) => {
  return (
    <Card>
      <Card.Header>
        <h3>{trip.transitNumber}</h3>
      </Card.Header>
      <Card.Content>
        {/* Trip details */}
      </Card.Content>
    </Card>
  );
});
```

##### **2. useMemo and useCallback for Value Memoization**

```typescript
function TripList({ trips, filters }: TripListProps) {
  const filteredTrips = useMemo(() => {
    return trips.filter(trip =>
      filters.status ? trip.tripStatus === filters.status : true
    );
  }, [trips, filters]);

  const handleTripClick = useCallback((tripId: string) => {
    // Handle trip selection
  }, []);

  return (
    <div>
      {filteredTrips.map(trip => (
        <TripCard key={trip.id} trip={trip} onClick={handleTripClick} />
      ))}
    </div>
  );
}
```

## Data Architecture Analysis

### Master Data Entities

#### 1. Driver Entity

```json
{
  "driverId": "string",
  "driverName": "string",
  "driverPassportNumber": "string",
  "driverNationality": "string",
  "driverContactNumber": "string",
  "licenseNumber": "string",
  "licenseExpiryDate": "date",
  "status": "active | inactive | suspended"
}
```

#### 2. Vehicle Entity

```json
{
  "vehicleId": "string",
  "vehiclePlateNumber": "string",
  "vehicleType": "truck | trailer | container",
  "vehicleModel": "string",
  "vehicleYear": "number",
  "trackerId": "string",
  "trackerType": "GPS | RFID | Cellular",
  "registrationCountry": "string",
  "capacity": {
    "weight": "number",
    "volume": "number"
  }
}
```

#### 3. Port/Location Entity

```json
{
  "locationId": "string",
  "locationName": "string",
  "locationType": "airport | seaport | landport | checkpoint | policestation | customsborder",
  "locationCode": "string",
  "coordinates": {
    "latitude": "number",
    "longitude": "number"
  },
  "address": "string",
  "country": "string",
  "operatingHours": {
    "open": "time",
    "close": "time"
  },
  "contactInfo": {
    "phone": "string",
    "email": "string"
  }
}
```

#### 4. Route Entity

```json
{
  "routeId": "string",
  "routeName": "string",
  "originPort": "string",
  "destinationPort": "string",
  "intermediatePoints": ["string"],
  "estimatedDistance": "number",
  "estimatedDuration": "number",
  "routeType": "domestic | international",
  "geofenceCoordinates": ["coordinates"],
  "restrictions": {
    "vehicleTypes": ["string"],
    "timeWindows": ["timeRange"]
  }
}
```

#### 5. Shipment Entity

```json
{
  "shipmentId": "string",
  "shipmentDescription": "string",
  "shipmentType": "import | export | transit",
  "cargoType": "general | hazardous | perishable | valuable",
  "weight": "number",
  "volume": "number",
  "value": "number",
  "currency": "string",
  "customsDeclarationNumber": "string",
  "documentReferences": ["string"]
}
```

## Entity Relationship Diagram

```mermaid
erDiagram
    DRIVER ||--o{ TRIP : drives
    VEHICLE ||--o{ TRIP : assigned_to
    ROUTE ||--o{ TRIP : follows
    SHIPMENT ||--|| TRIP : transported_in
    TRIP ||--o{ TRIP_ALERT : generates
    LOCATION ||--o{ ROUTE : includes
  
    DRIVER {
        string driverId PK
        string driverName
        string passportNumber
        string nationality
        string contactNumber
        string licenseNumber
        date licenseExpiry
        enum status
    }
  
    VEHICLE {
        string vehicleId PK
        string plateNumber
        string vehicleType
        string model
        number year
        string trackerId
        string trackerType
        string registrationCountry
    }
  
    LOCATION {
        string locationId PK
        string locationName
        enum locationType
        string locationCode
        decimal latitude
        decimal longitude
        string address
        string country
    }
  
    ROUTE {
        string routeId PK
        string routeName
        string originPort FK
        string destinationPort FK
        number estimatedDistance
        number estimatedDuration
        enum routeType
    }
  
    SHIPMENT {
        string shipmentId PK
        string description
        enum shipmentType
        enum cargoType
        number weight
        number volume
        number value
        string currency
    }
  
    TRIP {
        string tripId PK
        string tripNumber
        enum tripStatus
        enum tripType
        datetime creationDate
        datetime activationDate
        datetime completionDate
        string driverId FK
        string vehicleId FK
        string routeId FK
        string shipmentId FK
    }
  
    TRIP_ALERT {
        string alertId PK
        enum alertType
        enum alertStatus
        string tripId FK
        number transitSequence
        datetime timestamp
        decimal latitude
        decimal longitude
        string address
        enum severity
        string description
    }
```

## Data Flow Architecture

### Trip Lifecycle Flow

```mermaid
sequenceDiagram
    participant TC as Transportation Company
    participant TMS as Trip Management System
    participant ZD as ZATCA Dashboard
    participant ZO as ZATCA Officer
    participant TE as Tracking Engine
    participant AE as Alert Engine

    TC->>TMS: Register Trip & Submit Documents
    TMS->>ZD: Trip Appears in Dashboard
    ZO->>ZD: Monitor Assigned Ports
    TMS->>TE: Activate GPS Tracking
    TE->>AE: Start Real-time Monitoring

    loop Continuous Monitoring
        TE->>AE: Location & Sensor Updates
        AE->>AE: Evaluate Security Rules
        alt Alert Condition Detected
            AE->>ZD: Generate Real-time Alert
            ZD->>ZO: Display Alert Notification
            ZO->>ZD: Acknowledge Alert
            opt Critical Alert
                ZO->>ZO: Coordinate Response
            end
        end
        AE->>ZD: Update Trip Status
    end

    TC->>TMS: Complete Trip
    TMS->>TE: Stop Tracking
    TMS->>ZD: Final Trip Report
    ZO->>ZD: Review Completion
```

### Alert Processing Flow

```mermaid
flowchart TD
    A[IoT Device Data] --> B{Security Rule Evaluation}
    B -->|Normal Operation| C[Continue Monitoring]
    B -->|Security Violation| D[Generate Alert]
    D --> E{Alert Severity}
    E -->|Low| F[Dashboard Notification]
    E -->|Medium| G[ZATCA Officer Alert]
    E -->|High| H[Supervisor Escalation]
    E -->|Critical| I[Emergency Protocol]

    F --> J[ZATCA Dashboard Update]
    G --> K[Officer Notification]
    H --> L[Multi-level Alerts]
    I --> M[Immediate Response Team]

    K --> J
    L --> J
    M --> J

    J --> N[Officer Views Alert]
    N --> O{Officer Action}
    O -->|Acknowledge| P[Mark as Acknowledged]
    O -->|Investigate| Q[Assign Investigation]
    O -->|Resolve| R[Close Alert]
    O -->|Escalate| S[Forward to Supervisor]

    P --> T[Track Response Time]
    Q --> T
    R --> U[Update Trip Status]
    S --> H

    T --> V[Generate Activity Log]
    U --> V
    V --> W[Update Officer Statistics]
```

## User Management System

### User Entity

```json
{
  "userId": "string",
  "employeeName": "string",
  "employeeNameArabic": "string",
  "email": "string",
  "role": "super_user | admin | location_monitor | location_monitor_user",
  "organization": "customs | zatca | technical_united | other",
  "status": "active | inactive | suspended",
  "createdDate": "datetime",
  "lastLoginDate": "datetime",
  "assignedPorts": ["string"],
  "permissions": {
    "canViewTrips": "boolean",
    "canManageTrips": "boolean",
    "canViewAlerts": "boolean",
    "canManageAlerts": "boolean",
    "canManageUsers": "boolean",
    "canAssignPorts": "boolean",
    "canViewReports": "boolean",
    "canExportData": "boolean"
  },
  "profile": {
    "department": "string",
    "phoneNumber": "string",
    "preferredLanguage": "ar | en",
    "timezone": "string"
  }
}
```

### User Role Hierarchy

```mermaid
graph TD
    A[Super User] --> B[Admin]
    B --> C[Location Monitor]
    C --> D[Location Monitor User]

    A --> E[Full System Access]
    B --> F[User Management + Operations]
    C --> G[Port Monitoring + Trip Oversight]
    D --> H[Basic Monitoring + Alerts]
```

### User Activity Tracking Entity

```json
{
  "activityId": "string",
  "userId": "string",
  "activityType": "trip_viewed | trip_created | trip_updated | alert_read | alert_acknowledged | alert_resolved | user_login | user_logout | port_assigned | report_generated",
  "activityDescription": "string",
  "activityDescriptionArabic": "string",
  "timestamp": "datetime",
  "sessionId": "string",
  "ipAddress": "string",
  "userAgent": "string",
  "relatedEntityId": "string",
  "relatedEntityType": "trip | alert | user | port | route",
  "metadata": {
    "additionalData": "object"
  }
}
```

### User Activity Summary Entity

```json
{
  "summaryId": "string",
  "userId": "string",
  "periodStart": "date",
  "periodEnd": "date",
  "activityCounts": {
    "tripsViewed": "number",
    "tripsViewedArabic": "الرحلات المشاهده",
    "totalTripActivities": "number",
    "totalTripActivitiesArabic": "إجمالي أنشطة الرحلات",
    "alertsRead": "number",
    "alertsReadArabic": "التنبيهات المقروءة",
    "alertsAcknowledged": "number",
    "alertsResolved": "number",
    "reportsGenerated": "number",
    "loginSessions": "number"
  },
  "generatedAt": "datetime",
  "generatedBy": "string"
}
```

### User Port Assignment Entity

```json
{
  "assignmentId": "string",
  "userId": "string",
  "portId": "string",
  "assignedBy": "string",
  "assignedDate": "datetime",
  "status": "active | inactive | revoked",
  "permissions": {
    "canMonitor": "boolean",
    "canManageTrips": "boolean",
    "canViewReports": "boolean",
    "canReceiveAlerts": "boolean"
  },
  "notes": "string",
  "revokedDate": "datetime",
  "revokedBy": "string",
  "revokedReason": "string"
}
```

### User Management System Components

#### 1. Authentication & Authorization

- **Multi-factor Authentication**: SMS, Email, or Authenticator app
- **Role-based Access Control**: Hierarchical permission system
- **Session Management**: Secure session handling with timeout
- **Password Policy**: Complex password requirements and rotation

#### 2. User Activity Monitoring

- **Real-time Activity Tracking**: All user actions logged
- **Activity Summaries**: Periodic reports of user engagement
- **Audit Trail**: Comprehensive logging for compliance
- **Performance Metrics**: User productivity and system usage

#### 3. Port Assignment Management

- **Dynamic Port Assignment**: Flexible assignment of monitoring responsibilities
- **Permission Granularity**: Fine-grained access control per port
- **Assignment History**: Complete audit trail of assignments
- **Bulk Operations**: Mass assignment and revocation capabilities

#### 4. User Roles and Permissions

##### Super User

- Full system administration access
- User management and role assignment
- System configuration and maintenance
- Global reporting and analytics
- All operational permissions

##### Admin

- User management within organization
- Trip and alert management
- Port assignment capabilities
- Advanced reporting features
- System monitoring access

##### Location Monitor

- Port-specific monitoring oversight
- Trip supervision and management
- Alert escalation and resolution
- Team coordination and reporting
- Limited user management

##### Location Monitor User

- Basic monitoring operations
- Trip viewing and basic updates
- Alert acknowledgment and basic response
- Standard reporting access
- Read-only system access

### User Management Data Flow

```mermaid
sequenceDiagram
    participant A as Admin
    participant UMS as User Management System
    participant AS as Activity Service
    participant PS as Port Service
    participant NS as Notification Service

    A->>UMS: Create User
    UMS->>AS: Log User Creation
    A->>UMS: Assign Ports
    UMS->>PS: Update Port Assignments
    PS->>NS: Notify User of Assignment

    loop Daily Activity
        UMS->>AS: Track User Activities
        AS->>AS: Generate Activity Summary
    end

    A->>UMS: Request Activity Report
    UMS->>AS: Fetch Activity Data
    AS-->>UMS: Return Summary
    UMS-->>A: Display Report
```

### Operational/Transaction Entities

#### 1. Trip Entity

```json
{
  "tripId": "string",
  "tripNumber": "string",
  "tripStatus": "activated | ended | Not Charging",
  "tripType": "import | export | transit",
  "creationDate": "datetime",
  "activationDate": "datetime",
  "completionDate": "datetime",
  "driver": {
    "driverId": "string",
    "driverName": "string",
    "driverPassportNumber": "string",
    "driverNationality": "string"
  },
  "vehicle": {
    "vehicleId": "string",
    "vehiclePlateNumber": "string",
    "trackerId": "string"
  },
  "route": {
    "routeId": "string",
    "routeName": "string",
    "entryPort": "string",
    "exitPort": "string"
  },
  "shipment": {
    "shipmentId": "string",
    "shipmentDescription": "string"
  },
  "tracking": {
    "currentLocation": {
      "latitude": "number",
      "longitude": "number",
      "timestamp": "datetime"
    },
    "completedDistance": "number",
    "remainingDistance": "number",
    "estimatedArrival": "datetime"
  },
  "compliance": {
    "securityNotes": "string",
    "customsStatus": "cleared | pending | hold",
    "documentStatus": "complete | incomplete"
  }
}
```

#### 2. Trip Alert Entity

```json
{
  "alertId": "string",
  "alertType": "tracker_dropped | lock_tamper | lock_open | lock_connection_lost | tracker_battery_low | lock_low_battery | lock_very_low_battery | gsm_signal_lost | gps_signal_lost | geofence_entry_breach | geofence_exit_breach | tracker_connection_lost | trip_distance_exceeded | trip_time_exceeded | over_speeding | truck_stopped | wrong_direction | in_custom_area | entry_into_customs_area | truck_moved | four_hours_exceeded | suspected_area",
  "alertStatus": "acknowledged | triggered | resolved",
  "tripId": "string",
  "transitSequence": "number",
  "routeName": "string",
  "shipmentDescription": "string",
  "timestamp": "datetime",
  "location": {
    "latitude": "number",
    "longitude": "number",
    "address": "string"
  },
  "severity": "low | medium | high | critical",
  "description": "string",
  "acknowledgedBy": "string",
  "acknowledgedAt": "datetime",
  "resolvedBy": "string",
  "resolvedAt": "datetime",
  "comments": "string",
  "relatedData": {
    "speed": "number",
    "batteryLevel": "number",
    "signalStrength": "number",
    "geofenceId": "string"
  }
}
```

## System Components Architecture

### 1. Real-Time Monitoring Dashboard

- **Trip Statistics**: Active trips, trips with alerts, communication lost
- **Geographic Visualization**: Saudi Arabia map with location markers
- **Alert Management**: Real-time alert notifications and status tracking
- **Performance Metrics**: System uptime, response times, data accuracy

### 2. Trip Management Module

- **Trip Lifecycle**: Creation, activation, monitoring, completion
- **Route Planning**: Predefined routes with geofencing
- **Driver Assignment**: Driver-vehicle-trip associations
- **Document Management**: Customs declarations, permits, certificates

### 3. Alert Processing Engine

- **Real-Time Detection**: Continuous monitoring of tracking devices
- **Rule Engine**: Configurable alert rules and thresholds
- **Escalation Matrix**: Automatic escalation based on severity and time
- **Notification System**: Multi-channel alerts (SMS, email, dashboard)

### 4. Geospatial Services

- **Location Tracking**: GPS coordinate processing and validation
- **Geofencing**: Virtual boundary monitoring and breach detection
- **Route Optimization**: Dynamic route calculation and updates
- **Map Integration**: Google Maps API for visualization

### 5. Integration Layer

- **Customs Systems**: Electronic data interchange with customs authorities
- **Port Management**: Integration with port operational systems
- **Tracking Devices**: IoT device communication protocols
- **External APIs**: Weather, traffic, and regulatory data sources

## Technical Specifications

### Database Schema Requirements

- **Primary Database**: PostgreSQL with PostGIS extension for geospatial data
- **Cache Layer**: Redis for real-time data and session management
- **Time Series Database**: InfluxDB for tracking data and metrics
- **Document Store**: MongoDB for unstructured data and logs

### API Architecture

- **RESTful Services**: Standard CRUD operations for master data
- **WebSocket Connections**: Real-time updates for tracking and alerts
- **GraphQL Endpoint**: Flexible data querying for dashboard components
- **Webhook Integration**: External system notifications and callbacks

### Security Framework

- **Authentication**: Multi-factor authentication with role-based access
- **Authorization**: Granular permissions for data access and operations
- **Data Encryption**: End-to-end encryption for sensitive information
- **Audit Trail**: Comprehensive logging of all system activities

### Performance Requirements

- **Response Time**: < 2 seconds for dashboard updates
- **Throughput**: Support for 10,000+ concurrent trips
- **Availability**: 99.9% uptime with disaster recovery
- **Scalability**: Horizontal scaling for increased load

## Integration Points

### External Systems

1. **Saudi Customs Authority**: Electronic customs declarations and clearances
2. **Port Authorities**: Vessel schedules and cargo manifests
3. **Transportation Companies**: Fleet management and driver information
4. **Regulatory Bodies**: Compliance reporting and audit trails

### Data Exchange Formats

- **EDI Standards**: UN/EDIFACT for customs and trade documents
- **JSON APIs**: Modern web service integration
- **XML Schemas**: Legacy system compatibility
- **CSV Exports**: Reporting and data analysis

## User Management Integration

### User Management API Endpoints

### Sample User Data (Based on Screenshot Analysis)

### Sample Activity Summary Data

```json
{
  "activitySummary": {
    "userId": "usr_001",
    "periodStart": "2025-07-01",
    "periodEnd": "2025-07-31",
    "activityCounts": {
      "tripsViewed": 57,
      "tripsViewedArabic": "الرحلات المشاهده",
      "totalTripActivities": 19,
      "totalTripActivitiesArabic": "إجمالي أنشطة الرحلات",
      "alertsRead": 0,
      "alertsReadArabic": "التنبيهات المقروءة",
      "alertsAcknowledged": 0,
      "alertsResolved": 0,
      "reportsGenerated": 3,
      "loginSessions": 24
    },
    "generatedAt": "2025-07-31T23:59:59Z",
    "generatedBy": "system"
  }
}
```

## Implementation Roadmap

```mermaid
flowchart LR
    A[Design Phase] --> B[Frontend Development]
    B --> C[Backend Integration]
    C --> D[Production Deployment]

    A --> A1[UI/UX Design]
    A --> A2[Component Architecture]

    B --> B1[Dashboard Modules]
    B --> B2[Mock Data APIs]

    C --> C1[Real APIs]
    C --> C2[Live Data]

    D --> D1[Testing]
    D --> D2[Go Live]
```

### Phase 1: Design Phase

- Create UI/UX designs for all dashboard modules
- Define component architecture and design system
- Design responsive layouts for mobile and desktop
- Design RTL | LTR support for internalization Arabic | English

### Phase 2: Frontend Development

- Build all 6 dashboard modules with mock data
- Implement authentication and navigation
- Create mock API layer for backend simulation

### Phase 3: Backend Integration

- Replace mock APIs with real backend services
- Connect to IoT devices and transportation systems
- Implement production security and authentication

### Phase 4: Production Deployment

- Test with real data and live systems
- Deploy to production environment
- Train users and provide ongoing support
