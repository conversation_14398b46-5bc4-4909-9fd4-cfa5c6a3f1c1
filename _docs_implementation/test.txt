as fontend expert  in next.js 15 + please read full prompt requirement file here @/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/tts_template/_docs_ai_prompt/01_layout_prompt.md 
 to implement project after make good analysis

use image attached as app layout refrerence
file name 02_app_layout.png  

use logo to decide brand color and use this image in my app file name 03_app_logo.png

when development frontend don't miss rules in file to apply best practise
@/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/tts_template/_docs_ai_prompt/04_ai_agent_rules.md 

finall all our context docs you will find it in @/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/tts_template/_docs_ai_prompt/ 

after finished document implementation here
in folder @/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/tts_template/_docs_implementation/ 

finally run project in terminal and fix issues appears