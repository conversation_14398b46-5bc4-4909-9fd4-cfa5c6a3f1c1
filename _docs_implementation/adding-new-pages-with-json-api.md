# Adding New Pages with JSON API Integration

This guide explains how to add new pages to the TTS Template application with JSON-based API integration, following the pattern established by the Shipment Alerts Dashboard.

## Overview

The TTS Template uses a modular approach for data-driven pages:
- **Static JSON files** for data storage (easily replaceable with real APIs)
- **API layer** for data access and transformation
- **Reusable components** for UI consistency
- **TypeScript** for type safety

## Step-by-Step Guide

### 1. Create Your Data Structure

First, create a JSON file in the `/data` directory with your data structure.

**Example: `/data/your-feature.json`**
```json
{
  "summary": {
    "totalItems": 100,
    "activeItems": 85,
    "completedItems": 15
  },
  "items": [
    {
      "id": "ITEM-001",
      "title": "Sample Item",
      "description": "Description of the item",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00Z",
      "category": "important"
    }
  ]
}
```

### 2. Create API Layer

Create an API module in the `/api` directory to handle data access.

**Example: `/api/your-feature.ts`**
```typescript
import data from '../data/your-feature.json';

// Define TypeScript interfaces
export interface YourItem {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'pending';
  createdAt: string;
  category: string;
}

export interface YourSummary {
  totalItems: number;
  activeItems: number;
  completedItems: number;
}

export interface YourResponse {
  summary: YourSummary;
  items: YourItem[];
}

export interface SearchFilters {
  query?: string;
  status?: string;
  category?: string;
}

// Simulate API delay (use 0 for static data to avoid flickering)
const simulateApiDelay = (ms: number = 0): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Main API function
export async function getYourData(filters?: SearchFilters): Promise<YourResponse> {
  try {
    await simulateApiDelay();
    
    let filteredItems = [...data.items];
    
    // Apply filters
    if (filters?.query) {
      const query = filters.query.toLowerCase();
      filteredItems = filteredItems.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query)
      );
    }
    
    if (filters?.status) {
      filteredItems = filteredItems.filter(item => item.status === filters.status);
    }
    
    if (filters?.category) {
      filteredItems = filteredItems.filter(item => item.category === filters.category);
    }
    
    // Recalculate summary
    const summary: YourSummary = {
      totalItems: filteredItems.length,
      activeItems: filteredItems.filter(i => i.status === 'active').length,
      completedItems: filteredItems.filter(i => i.status === 'completed').length,
    };
    
    return { summary, items: filteredItems };
  } catch (error) {
    console.error('Error fetching data:', error);
    throw new Error('Failed to fetch data');
  }
}
```

### 3. Create Reusable Components

Create components for your UI elements in the `/components` directory.

**Optional: Add View Toggle for Table/List Views**

You can add multiple view options like the shipment dashboard:

```typescript
// ViewToggle.tsx
import { Table, List } from 'lucide-react';

interface ViewToggleProps {
  view: 'table' | 'list';
  onViewChange: (view: 'table' | 'list') => void;
}

export default function ViewToggle({ view, onViewChange }: ViewToggleProps) {
  return (
    <div className="flex items-center bg-gray-100 rounded-lg p-1">
      <button onClick={() => onViewChange('list')} className={...}>
        <List className="w-4 h-4" />
        <span>List View</span>
      </button>
      <button onClick={() => onViewChange('table')} className={...}>
        <Table className="w-4 h-4" />
        <span>Table View</span>
      </button>
    </div>
  );
}
```

**Example: `/components/YourItemCard.tsx`**
```typescript
import React from 'react';
import { YourItem } from '../api/your-feature';

interface YourItemCardProps {
  item: YourItem;
}

export default function YourItemCard({ item }: YourItemCardProps) {
  return (
    <div className="bg-white rounded-lg border p-6 hover:shadow-md transition-shadow">
      <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
      <p className="text-gray-600 mb-4">{item.description}</p>
      <div className="flex justify-between items-center">
        <span className={`px-2 py-1 rounded text-sm ${
          item.status === 'active' ? 'bg-green-100 text-green-800' :
          item.status === 'completed' ? 'bg-blue-100 text-blue-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {item.status}
        </span>
        <span className="text-sm text-gray-500">
          {new Date(item.createdAt).toLocaleDateString()}
        </span>
      </div>
    </div>
  );
}
```

### 4. Create the Page

Create your page in the `/app` directory following Next.js 13+ app router conventions.

**Example: `/app/your-feature/page.tsx`**
```typescript
'use client';

import React, { useState, useEffect } from 'react';
import { getYourData, type YourResponse, type SearchFilters } from '../../api/your-feature';
import YourItemCard from '../../components/YourItemCard';
import SummaryCard from '../../components/SummaryCard';
import { TrendingUp, CheckCircle, Clock } from 'lucide-react';

export default function YourFeaturePage() {
  const [data, setData] = useState<YourResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async (filters?: SearchFilters) => {
    try {
      setLoading(true);
      setError(null);
      const response = await getYourData(filters);
      setData(response);
    } catch (err) {
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  if (error) {
    return <div className="text-red-600">Error: {error}</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Your Feature Dashboard</h1>
        
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
          <SummaryCard
            label="Total Items"
            count={data?.summary.totalItems || 0}
            icon={TrendingUp}
            variant="default"
          />
          <SummaryCard
            label="Active Items"
            count={data?.summary.activeItems || 0}
            icon={Clock}
            variant="warning"
          />
          <SummaryCard
            label="Completed Items"
            count={data?.summary.completedItems || 0}
            icon={CheckCircle}
            variant="success"
          />
        </div>

        {/* Items List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            [...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg border p-6 animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))
          ) : (
            data?.items.map(item => (
              <YourItemCard key={item.id} item={item} />
            ))
          )}
        </div>
      </div>
    </div>
  );
}
```

### 5. Add Navigation (Optional)

Add a link to your new page in the home page or navigation menu.

**In `/app/page.tsx`:**
```typescript
<Link href="/your-feature" className="...">
  Your Feature Dashboard
</Link>
```

## Migration to Real Backend API

When you're ready to replace the JSON files with real backend APIs:

1. **Update the API functions** in `/api/your-feature.ts`:
   ```typescript
   export async function getYourData(filters?: SearchFilters): Promise<YourResponse> {
     const response = await fetch('/api/backend/your-feature', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify(filters)
     });
     
     if (!response.ok) {
       throw new Error('Failed to fetch data');
     }
     
     return response.json();
   }
   ```

2. **Keep the same interfaces** - your components won't need to change
3. **Add error handling** and loading states as needed
4. **Update any hardcoded delays** or simulation logic

## Best Practices

1. **Type Safety**: Always define TypeScript interfaces for your data
2. **Error Handling**: Implement proper error states in your components
3. **Loading States**: Show loading indicators for better UX
4. **Reusable Components**: Create modular components that can be reused
5. **Consistent Styling**: Follow the existing design patterns
6. **Documentation**: Document your API functions and component props

## File Structure Summary

```
your-project/
├── data/
│   └── your-feature.json          # Static data
├── api/
│   └── your-feature.ts            # API layer
├── components/
│   ├── YourItemCard.tsx           # Feature-specific components
│   └── SummaryCard.tsx            # Reusable components
├── app/
│   └── your-feature/
│       └── page.tsx               # Main page component
└── _docs_implementation/
    └── your-feature-docs.md       # Feature documentation
```

This pattern ensures maintainable, scalable code that can easily transition from static JSON to real backend APIs.
