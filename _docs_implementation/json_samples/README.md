# JSON Sample Data for TTS Architecture

This directory contains sample JSON data files that correspond to the entities defined in the TTS technical architecture documentation.

## Entity Files

### Master Data Entities

1. **driver.json** - Driver information including personal details, license information, and status
2. **vehicle.json** - Vehicle specifications, tracking device information, and capacity details
3. **ports.json** - Port/location data including airports, seaports, land ports, checkpoints, and police stations
4. **routes.json** - Route definitions with geofencing coordinates and restrictions
5. **users.json** - ZATCA user accounts with roles, permissions, and assigned ports

### Transaction/Operational Entities

6. **trips.json** - Trip records with driver, vehicle, route, and shipment information
7. **trip_alerts.json** - Security alerts generated during trip monitoring
8. **user_activity_summary.json** - User activity tracking and performance metrics

### Dashboard-Specific Data

9. **my_assigned_ports.json** - User port assignments with statistics and recent activity
10. **suspicious_trips.json** - Flagged trips with risk assessment and investigation status

## Data Structure Features

### Bilingual Support
- Arabic and English field names where applicable
- Arabic translations for user-facing content
- RTL/LTR language support considerations

### ZATCA-Specific Fields
- Government organization codes (customs.gov.sa, zatca.gov.sa)
- Saudi Arabia geographic coordinates and addresses
- Role-based permissions aligned with ZATCA hierarchy

### Real-World Data Patterns
- Authentic Saudi location names and coordinates
- Realistic trip routes and timing
- Government email domains and phone number formats
- Arabic names and translations

## Usage in Frontend Development

These JSON files serve as:
- **Mock API responses** during frontend development
- **Component testing data** for UI development
- **Demo data** for stakeholder presentations
- **Backend API specification** examples

## Integration with TTS Template

The data structure aligns with:
- Existing mock data patterns in the TTS template
- Component prop interfaces and TypeScript types
- API endpoint specifications
- Dashboard module requirements

## Sample Data Highlights

### My Assigned Ports Dashboard
- Port assignment details with permissions
- Real-time statistics (active trips, alerts)
- Recent activity logs with bilingual descriptions
- Available ports for assignment requests

### Suspicious Trips Dashboard
- Risk scoring and suspicion levels (critical, high, medium, low)
- Multiple risk factors (driver, route, cargo, behavior)
- Investigation status tracking
- Real-time location and alert integration
- Arabic/English reason descriptions

These samples provide a comprehensive foundation for frontend development while maintaining realistic data patterns that reflect actual ZATCA operational requirements.
