# My Assigned Ports Dashboard - Frontend Specification

## Visual Analysis from Image

Based on the provided screenshot, this page displays a trip monitoring dashboard with the following UX elements:

### Header Statistics Section

The top section contains 5 summary cards displaying:

1. **Current Date/Time**: "1447/01/20 04:00:32" (Hijri calendar format) with clock icon
2. **Active Trips with Alerts**: "11" with orange bell notification icon
3. **Active Trips without Alerts**: "1551" with gray bell icon
4. **Total Active Trips**: "1562" with green checkmark icon
5. **Total Closed Trips**: "0" with red prohibition icon

## Screen Layout Visualization

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                    My Assigned Ports Dashboard                                                     │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐        │
│  │  🕐 Current Time │  │  🔔 Active Trips│  │  🔔 Active Trips│  │  ✅ Total Active│  │  🚫 Total Closed│        │
│  │                 │  │   with <PERSON><PERSON><PERSON>   │  │  without Alerts   │  │      Trips      │  │      Trips      │        │
│  │ 1447/01/20      │  │       11        │  │      1551       │  │      1562       │  │        0        │        │
│  │   04:00:32      │  │                 │  │                 │  │                 │  │                 │        │
│  │   (Hijri)       │  │                 │  │                 │  │                 │  │                 │        │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘        │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│  [📥 Download Trip Panel as XLS]                                                                                   │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ Details │Transit│ Shipment Description │ Entry Port-Exit Port │Last Seen│Tracker│Driver Name│Location │Details│Stats│
│ تفاصيل  │ رقم   │    وصف الشحنة        │ ميناء الدخول-الخروج │آخر ظهور │المتتبع│ اسم السائق│ الموقع │التفاصيل│إحصائيات│
│ العبور │       │                      │                      │         │       │           │         │       │الرحلة │
├─────────┼───────┼──────────────────────┼──────────────────────┼─────────┼───────┼───────────┼─────────┼───────┼─────┤
│   ⚠️    │   0   │ Shipment/Description │ King Fahad Int       │16539/   │15047/ │ Jomo      │ Abo     │تفاصيل │ 📊  │
│         │       │ .....                │ Airport - Khalij     │1332     │132    │ sudan     │ Dhabi   │الرحلة │     │
│         │       │                      │ Border               │         │       │           │         │       │     │
│         │       │                      │ Target in Entry      │         │       │           │ ABC-123456789,│   │     │
│         │       │                      │ Border               │         │       │           │ Mosaik, Red,  │   │     │
│         │       │                      │                      │         │       │           │ Type, Plate   │   │     │
│         │       │                      │                      │         │       │           │ CountryName   │   │     │
├─────────┼───────┼──────────────────────┼──────────────────────┼─────────┼───────┼───────────┼─────────┼───────┼─────┤
│   ⚠️    │   0   │ Shipment/Description │ King Fahad Int       │16539/   │15047/ │ Jomo      │ Abo     │تفاصيل │ 📊  │
│         │       │ .....                │ Airport - Khalij     │1332     │132    │ sudan     │ Dhabi   │الرحلة │     │
│         │       │                      │ Border               │         │       │           │         │       │     │
│         │       │                      │ Target in Entry      │         │       │           │ ABC-123456789,│   │     │
│         │       │                      │ Border               │         │       │           │ Mosaik, Red,  │   │     │
│         │       │                      │                      │         │       │           │ Type, Plate   │   │     │
│         │       │                      │                      │         │       │           │ CountryName   │   │     │
├─────────┼───────┼──────────────────────┼──────────────────────┼─────────┼───────┼───────────┼─────────┼───────┼─────┤
│   🔴    │   0   │ Shipment/Description │ Khalij Islamic       │16539/   │15047/ │ Jomo      │ Wadi    │تفاصيل │ 📊  │
│         │       │ .....                │ Seaport - Khalij     │1332     │132    │ sudan     │ Aziza   │الرحلة │     │
│         │       │                      │ Airport              │         │       │           │         │       │     │
│         │       │                      │ Target in Entry      │         │       │           │ ABC-123456789,│   │     │
│         │       │                      │ Border               │         │       │           │ Mosaik, Red,  │   │     │
│         │       │                      │                      │         │       │           │ Type, Plate   │   │     │
│         │       │                      │                      │         │       │           │ CountryName   │   │     │
└─────────┴───────┴──────────────────────┴──────────────────────┴─────────┴───────┴───────────┴─────────┴───────┴─────┘
```

### Legend:

- 🕐 **Clock Icon**: Current date/time in Hijri calendar
- 🔔 **Bell Icons**: Orange (urgent alerts), Gray (standard alerts)
- ✅ **Checkmark**: Completed/successful trips
- 🚫 **Prohibition**: Closed/cancelled trips
- ⚠️ **Warning Circle**: Trips with alerts (yellow)
- 🔴 **Red Circle**: Critical status trips
- 📥 **Download Icon**: Export functionality
- 📊 **Chart Icon**: Trip statistics and actions

### Download Action

Blue button labeled "Download Trip Panel as XLS" for exporting data

### Main Data Table

The table displays trip information with the following columns:

- **Details** (تفاصيل): Yellow warning indicators
- **Transit Number** (رقم العبور): Sequential numbers (0)
- **Shipment Description** (وصف الشحنة): "Shipment/Description....."
- **Entry Port-Exit Port** (ميناء الدخول-ميناء الخروج): "King Fahad Int Airport - Khalij Border" and "Target in Entry Border"
- **Last Seen** (آخر ظهور): Timestamps like "16539/1332", "15047/132"
- **Tracker** (المتتبع): Tracker IDs
- **Driver Name** (اسم السائق): "Jomo sudan"
- **Abo Dhabi** (أبو ظبي): Location references with coordinates
- **التفاصيل** (Details): Arabic details column
- **Trip Statistics** (إحصائيات الرحلة): Action icons

### Row Status Indicators

- Yellow warning circles indicating alert status
- Red highlighted row showing "Khalij Islamic Seaport - Khalij Airport" with "Wadi Aziza" location

## Data Structure for Frontend Implementation

Based on the trip entity structure and visual elements, the JSON structure should be:

```json
{
  "dashboardSummary": {
    "statistics": {
      "activeTripsWithAlerts": 11,
      "totalActiveTripsWithAlerts": 1551,
      "totalActiveTrips": 1562,
      "totalClosedTrips": 0
    }
  },
  "trips": [
    {
      "tripId": "TRP001",
      "tripNumber": "14471/01/20",
      "tripStatus": "activated",
      "alertStatus": "warning",
      "transitNumber": 0,
      "shipment": {
        "shipmentDescription": "Shipment/Description....."
      },
      "route": {
        "entryPort": "King Fahad Int Airport",
        "exitPort": "Khalij Border",
        "routeName": "Target in Entry Border"
      },
      "tracking": {
        "lastSeen": "16539/1332",
        "trackerId": "15047/132",
        "currentLocation": {
          "locationName": "Abo Dhabi",
          "coordinates": "ABC-123456789, Mosaik, Red, Type, PlateCountryName"
        }
      },
      "driver": {
        "driverName": "Jomo sudan"
      },
      "details": "تفاصيل الرحلة",
      "tripStatistics": {
        "hasAlerts": true,
        "alertCount": 1
      }
    },
    {
      "tripId": "TRP002",
      "tripNumber": "15512/02/20",
      "tripStatus": "activated",
      "alertStatus": "critical",
      "transitNumber": 0,
      "shipment": {
        "shipmentDescription": "Shipment/Description....."
      },
      "route": {
        "entryPort": "Khalij Islamic Seaport",
        "exitPort": "Khalij Airport",
        "routeName": "Target in Entry Border"
      },
      "tracking": {
        "lastSeen": "16539/1332",
        "trackerId": "15047/132",
        "currentLocation": {
          "locationName": "Wadi Aziza",
          "coordinates": "ABC-123456789, Mosaik, Red, Type, PlateCountryName"
        }
      },
      "driver": {
        "driverName": "Jomo sudan"
      },
      "details": "تفاصيل الرحلة",
      "tripStatistics": {
        "hasAlerts": true,
        "alertCount": 2
      }
    }
  ],
  "tableConfig": {
    "columns": [
      "details",
      "transitNumber",
      "shipmentDescription",
      "entryPortExitPort",
      "lastSeen",
      "tracker",
      "driverName",
      "currentLocation",
      "detailsArabic",
      "tripStatistics"
    ],
    "sortable": true,
    "filterable": true,
    "exportable": true
  }
}
```

## Reusable Components Architecture

### 1. HijriClock Component (Client-Side Only)

```typescript
interface HijriClockProps {
  className?: string;
  showSeconds?: boolean;
  format?: 'full' | 'date-only' | 'time-only';
}

// This component handles Hijri date/time conversion on client-side
// No JSON data needed - uses browser Date API with Hijri conversion
```

### 2. DashboardSummaryCards Component (Reusable)

```typescript
interface SummaryCard {
  id: string;
  titleKey: string; // Translation key instead of hardcoded text
  value: number | string;
  icon: 'clock' | 'bell-orange' | 'bell-gray' | 'check' | 'prohibition' | 'custom';
  customIcon?: React.ReactNode;
  color: 'blue' | 'orange' | 'gray' | 'green' | 'red';
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'stable';
  };
}

interface DashboardSummaryCardsProps {
  cards: SummaryCard[];
  layout?: 'horizontal' | 'grid-2x3' | 'grid-3x2';
  showTrends?: boolean;
}

// Usage for different pages:
// - My Assigned Ports: 5 cards (clock + 4 statistics)
// - Location Monitor: 4 cards (statistics only)
// - Dashboard Overview: 6+ cards (comprehensive stats)
// - Suspicious Trips: 4 cards (risk-focused metrics)
```

### 3. DataTable Component (Highly Reusable)

```typescript
interface TableColumn {
  key: string;
  titleKey: string; // Translation key instead of hardcoded text
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
}

interface TableAction {
  key: string;
  labelKey: string; // Translation key for action label
  icon?: React.ReactNode;
  onClick: (row: any) => void;
  variant?: 'primary' | 'secondary' | 'danger';
  visible?: (row: any) => boolean;
}

interface DataTableProps<T = any> {
  // Data
  data: T[];
  columns: TableColumn[];

  // Table Configuration
  pageSize?: number;
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  selectable?: boolean;

  // Master-Detail Configuration
  expandable?: boolean;
  expandedRowRender?: (row: T, index: number) => React.ReactNode;
  expandedRowKeys?: string[]; // Controlled expansion
  onExpandedRowsChange?: (expandedKeys: string[]) => void;
  expandRowByClick?: boolean; // Expand on row click vs expand button only
  expandIconColumnIndex?: number; // Which column to show expand icon (default: 0)
  expandIcon?: (expanded: boolean) => React.ReactNode;
  defaultExpandAllRows?: boolean;
  expandedRowClassName?: string;

  // Actions
  actions?: TableAction[];
  bulkActions?: TableAction[];
  onRowClick?: (row: T) => void;
  onSelectionChange?: (selectedRows: T[]) => void;

  // Export
  exportable?: boolean;
  exportFormats?: ('xlsx' | 'csv' | 'pdf')[];
  exportFileNameKey?: string; // Translation key for export filename
  onExport?: (format: string, data: T[]) => void;

  // Status Indicators
  rowStatusField?: string; // Field name for row status
  statusConfig?: {
    [key: string]: {
      color: string;
      icon?: React.ReactNode;
      highlight?: boolean;
    };
  };

  // Loading & Error States
  loading?: boolean;
  error?: string;
  emptyMessageKey?: string; // Translation key for empty state message

  // Styling
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  striped?: boolean;
  bordered?: boolean;
}
```

## Master-Detail Feature Analysis

### Feature Overview
The master-detail pattern allows users to view summary information in the main table rows and expand individual rows to see detailed information without navigating to a separate page. This enhances user experience by providing contextual details on-demand.

### Use Cases for TTS Dashboard

#### 1. Trip Details Expansion
**Master Row**: Basic trip information (trip number, status, driver, route)
**Detail Panel**:
- Complete shipment details
- Real-time tracking information
- Alert history and timeline
- Route progress with map
- Driver and vehicle detailed information
- Compliance status and documents

#### 2. Alert Details Expansion
**Master Row**: Alert type, severity, timestamp
**Detail Panel**:
- Detailed alert description
- Location coordinates with mini-map
- Resolution steps and history
- Related alerts for same trip
- Recommended actions

#### 3. Port Assignment Details
**Master Row**: Port name, active trips, alert count
**Detail Panel**:
- Detailed port statistics
- Recent activity timeline
- Assigned personnel
- Port-specific configurations
- Performance metrics

### Implementation Specifications

#### Master-Detail Component Structure
```typescript
interface MasterDetailRowProps<T> {
  row: T;
  index: number;
  expanded: boolean;
  onToggleExpand: (key: string) => void;
  expandedContent: React.ReactNode;
  expandIconPosition: 'left' | 'right';
}

// Detail Panel Components for Different Data Types
interface TripDetailPanelProps {
  trip: Trip;
  onClose?: () => void;
  onActionClick?: (action: string, trip: Trip) => void;
}

interface AlertDetailPanelProps {
  alert: TripAlert;
  onResolve?: (alertId: string) => void;
  onAcknowledge?: (alertId: string) => void;
}

interface PortDetailPanelProps {
  port: Port;
  statistics: PortStatistics;
  recentActivity: Activity[];
}
```

#### Expansion Behavior Options
```typescript
type ExpansionMode =
  | 'single'      // Only one row can be expanded at a time
  | 'multiple'    // Multiple rows can be expanded simultaneously
  | 'accordion';  // Accordion-style expansion within groups

interface ExpansionConfig {
  mode: ExpansionMode;
  animationDuration?: number;
  expandOnRowClick?: boolean;
  expandIconPosition?: 'start' | 'end';
  persistExpansion?: boolean; // Remember expanded state
  lazyLoadDetails?: boolean;  // Load detail data only when expanded
}
```

### Visual Design Specifications

#### Expand/Collapse Icon States
```typescript
interface ExpandIconConfig {
  expandedIcon: React.ReactNode;   // ▼ or ⊖
  collapsedIcon: React.ReactNode;  // ▶ or ⊕
  position: 'first-column' | 'last-column' | 'custom';
  size: 'sm' | 'md' | 'lg';
  color: string;
  hoverColor: string;
}
```

#### Detail Panel Layout
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Master Row: Trip #14471/01/20 | Active | King Fahad Airport → Khalij Border │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─ Expanded Detail Panel ─────────────────────────────────────────────────┐ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│ │ │   Shipment  │ │   Tracking  │ │   Alerts    │ │      Actions        │ │ │
│ │ │   Details   │ │   Status    │ │   History   │ │                     │ │ │
│ │ │             │ │             │ │             │ │ [View Full Details] │ │ │
│ │ │ Description │ │ Current Loc │ │ 3 Active    │ │ [Generate Report]   │ │ │
│ │ │ Electronics │ │ Riyadh      │ │ 1 Resolved  │ │ [Contact Driver]    │ │ │
│ │ │ 150 kg      │ │ ETA: 14:30  │ │             │ │                     │ │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Master-Detail Usage Examples

#### 1. My Assigned Ports - Trip Master-Detail
```typescript
const MyAssignedPortsPage: React.FC = () => {
  const [expandedRows, setExpandedRows] = useState<string[]>([]);

  const handleExpandedRowsChange = (keys: string[]) => {
    setExpandedRows(keys);
  };

  const renderTripDetails = (trip: Trip) => (
    <TripDetailPanel
      trip={trip}
      onActionClick={(action, trip) => {
        switch(action) {
          case 'view-full':
            navigate(`/trip-details/${trip.tripId}`);
            break;
          case 'contact-driver':
            openDriverContact(trip.driver.driverId);
            break;
          case 'generate-report':
            generateTripReport(trip.tripId);
            break;
        }
      }}
    />
  );

  return (
    <DataTable
      data={trips}
      columns={myAssignedPortsColumns}
      expandable={true}
      expandedRowRender={renderTripDetails}
      expandedRowKeys={expandedRows}
      onExpandedRowsChange={handleExpandedRowsChange}
      expandRowByClick={false} // Only expand via button
      expandIconColumnIndex={0} // First column
      expandIcon={(expanded) => expanded ? <ChevronDownIcon /> : <ChevronRightIcon />}
    />
  );
};
```

#### 2. Suspicious Trips - Risk Assessment Details
```typescript
const SuspiciousTripsPage: React.FC = () => {
  const renderSuspiciousDetails = (trip: SuspiciousTrip) => (
    <SuspiciousTripDetailPanel
      trip={trip}
      onInvestigationAction={(action, tripId) => {
        switch(action) {
          case 'assign-investigator':
            assignInvestigator(tripId);
            break;
          case 'escalate':
            escalateToSupervisor(tripId);
            break;
          case 'mark-resolved':
            markAsResolved(tripId);
            break;
        }
      }}
    />
  );

  return (
    <DataTable
      data={suspiciousTrips}
      columns={suspiciousTripsColumns}
      expandable={true}
      expandedRowRender={renderSuspiciousDetails}
      expandRowByClick={true} // Expand on row click
      defaultExpandAllRows={false}
      expandIconColumnIndex={0}
    />
  );
};
```

### Performance Considerations

#### Lazy Loading Strategy
```typescript
interface LazyDetailConfig {
  enabled: boolean;
  loadingComponent: React.ReactNode;
  errorComponent: React.ReactNode;
  cacheDetails: boolean;
  maxCacheSize: number;
}

// Implementation
const renderLazyTripDetails = (trip: Trip) => {
  const [detailData, setDetailData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTripDetails(trip.tripId)
      .then(setDetailData)
      .finally(() => setLoading(false));
  }, [trip.tripId]);

  if (loading) return <DetailLoadingSkeleton />;
  return <TripDetailPanel trip={trip} details={detailData} />;
};
```

#### Virtual Scrolling with Expansion
```typescript
interface VirtualTableConfig {
  enabled: boolean;
  itemHeight: number;
  expandedItemHeight: number;
  overscan: number;
  maintainScrollPosition: boolean;
}
```

### Accessibility Features

#### Keyboard Navigation
```typescript
interface A11yConfig {
  expandKeyBinding: string; // Default: 'Enter' or 'Space'
  collapseKeyBinding: string;
  focusManagement: 'maintain' | 'move-to-detail' | 'move-to-next';
  announceExpansion: boolean;
  detailRegionLabel: string;
}
```

#### Screen Reader Support
```typescript
// ARIA attributes for expanded rows
const expandedRowProps = {
  'aria-expanded': expanded,
  'aria-controls': `detail-${rowId}`,
  'aria-label': t('table.row.expandDetails', { rowTitle })
};

const detailPanelProps = {
  id: `detail-${rowId}`,
  'aria-labelledby': `row-${rowId}`,
  role: 'region',
  'aria-live': 'polite'
};
```

```typescript
// Translation Keys for Table Columns
// Add these to translations/en.ts and translations/ar.ts

// English translations (translations/en.ts)
const tableTranslations = {
  'table.column.details': 'Details',
  'table.column.transitNumber': 'Transit Number',
  'table.column.shipmentDescription': 'Shipment Description',
  'table.column.entryExitPort': 'Entry Port-Exit Port',
  'table.column.lastSeen': 'Last Seen',
  'table.column.tracker': 'Tracker',
  'table.column.driverName': 'Driver Name',
  'table.column.location': 'Location',
  'table.column.tripStatistics': 'Trip Statistics',
  'table.column.riskScore': 'Risk Score',
  'table.column.tripNumber': 'Trip Number',
  'table.column.suspicionLevel': 'Suspicion Level',
  'table.column.flaggedDate': 'Flagged Date',
  'table.column.investigation': 'Investigation',
  'table.column.investigator': 'Investigator',
  'table.column.status': 'Status',
  'table.column.currentLocation': 'Current Location',
  'table.column.eta': 'ETA',
  'table.column.alerts': 'Alerts',
  'table.action.export': 'Export',
  'table.action.view': 'View',
  'table.action.edit': 'Edit',
  'table.empty.noData': 'No data available',
  'table.export.filename.myAssignedPorts': 'my-assigned-ports-trips',
  'table.export.filename.suspiciousTrips': 'suspicious-trips-report',
  'table.export.filename.locationMonitor': 'location-monitor-data'
};

// Arabic translations (translations/ar.ts)
const tableTranslationsAr = {
  'table.column.details': 'تفاصيل',
  'table.column.transitNumber': 'رقم العبور',
  'table.column.shipmentDescription': 'وصف الشحنة',
  'table.column.entryExitPort': 'ميناء الدخول-ميناء الخروج',
  'table.column.lastSeen': 'آخر ظهور',
  'table.column.tracker': 'المتتبع',
  'table.column.driverName': 'اسم السائق',
  'table.column.location': 'الموقع',
  'table.column.tripStatistics': 'إحصائيات الرحلة',
  'table.column.riskScore': 'نقاط المخاطر',
  'table.column.tripNumber': 'رقم الرحلة',
  'table.column.suspicionLevel': 'مستوى الشك',
  'table.column.flaggedDate': 'تاريخ الإبلاغ',
  'table.column.investigation': 'التحقيق',
  'table.column.investigator': 'المحقق',
  'table.column.status': 'الحالة',
  'table.column.currentLocation': 'الموقع الحالي',
  'table.column.eta': 'الوصول المتوقع',
  'table.column.alerts': 'التنبيهات',
  'table.action.export': 'تصدير',
  'table.action.view': 'عرض',
  'table.action.edit': 'تحرير',
  'table.empty.noData': 'لا توجد بيانات متاحة',
  'table.export.filename.myAssignedPorts': 'رحلات-الموانئ-المخصصة',
  'table.export.filename.suspiciousTrips': 'تقرير-الرحلات-المشبوهة',
  'table.export.filename.locationMonitor': 'بيانات-مراقب-الموقع'
};

// Usage Examples for Different Pages:

// 1. My Assigned Ports - Trip Table
const myAssignedPortsColumns: TableColumn[] = [
  { key: 'alertStatus', titleKey: 'table.column.details', render: (value) => <AlertIndicator status={value} /> },
  { key: 'transitNumber', titleKey: 'table.column.transitNumber', sortable: true },
  { key: 'shipmentDescription', titleKey: 'table.column.shipmentDescription' },
  { key: 'route', titleKey: 'table.column.entryExitPort', render: (value) => `${value.entryPort} - ${value.exitPort}` },
  { key: 'lastSeen', titleKey: 'table.column.lastSeen', sortable: true },
  { key: 'trackerId', titleKey: 'table.column.tracker' },
  { key: 'driverName', titleKey: 'table.column.driverName' },
  { key: 'currentLocation', titleKey: 'table.column.location' },
  { key: 'details', titleKey: 'table.column.details' },
  { key: 'actions', titleKey: 'table.column.tripStatistics', render: (_, row) => <TripActions trip={row} /> }
];

// 2. Suspicious Trips - Risk Table
const suspiciousTripsColumns: TableColumn[] = [
  { key: 'riskScore', titleKey: 'table.column.riskScore', sortable: true, render: (value) => <RiskBadge score={value} /> },
  { key: 'tripNumber', titleKey: 'table.column.tripNumber', sortable: true },
  { key: 'suspicionLevel', titleKey: 'table.column.suspicionLevel', filterable: true },
  { key: 'flaggedDate', titleKey: 'table.column.flaggedDate', sortable: true },
  { key: 'investigationStatus', titleKey: 'table.column.investigation', filterable: true },
  { key: 'assignedInvestigator', titleKey: 'table.column.investigator' }
];

// 3. Location Monitor - Live Trips Table
const locationMonitorColumns: TableColumn[] = [
  { key: 'status', titleKey: 'table.column.status', render: (value) => <StatusBadge status={value} /> },
  { key: 'tripNumber', titleKey: 'table.column.tripNumber' },
  { key: 'currentLocation', titleKey: 'table.column.currentLocation' },
  { key: 'estimatedArrival', titleKey: 'table.column.eta', sortable: true },
  { key: 'alertCount', titleKey: 'table.column.alerts', render: (value) => <AlertCount count={value} /> }
];
```

### 4. Page-Specific Data Filtering

```typescript
// API Service Layer for Different Pages
interface PageDataService {
  // My Assigned Ports - Filter by user's assigned ports
  getMyAssignedPortsData: (userId: string) => Promise<{
    statistics: DashboardStatistics;
    trips: Trip[];
  }>;

  // Location Monitor - Filter by real-time active trips
  getLocationMonitorData: (filters?: {
    portIds?: string[];
    status?: string[];
    alertLevel?: string[];
  }) => Promise<{
    statistics: DashboardStatistics;
    trips: Trip[];
  }>;

  // Suspicious Trips - Filter by risk level and investigation status
  getSuspiciousTripsData: (filters?: {
    riskLevel?: string[];
    investigationStatus?: string[];
    dateRange?: { from: Date; to: Date };
  }) => Promise<{
    statistics: DashboardStatistics;
    trips: SuspiciousTrip[];
  }>;

  // Focused Trips - Filter by priority and assignment
  getFocusedTripsData: (userId: string, filters?: {
    priority?: string[];
    assignedPorts?: string[];
  }) => Promise<{
    statistics: DashboardStatistics;
    trips: Trip[];
  }>;
}

// Page Component Pattern
interface PageComponentProps {
  // Navigation context
  currentPage: 'my-assigned-ports' | 'location-monitor' | 'suspicious-trips' | 'focused-trips';
  userId: string;
  userPermissions: string[];

  // Filtering
  defaultFilters?: Record<string, any>;
  availableFilters?: FilterConfig[];
}

// Filter Configuration
interface FilterConfig {
  key: string;
  type: 'select' | 'multiselect' | 'daterange' | 'search';
  label: string;
  labelArabic: string;
  options?: { value: string; label: string; labelArabic: string }[];
  defaultValue?: any;
}
```

### 5. Component Usage Examples

```typescript
// My Assigned Ports Page
const MyAssignedPortsPage: React.FC = () => {
  const { userId } = useAuth();
  const [data, setData] = useState(null);

  // Summary Cards Configuration with Translation Keys
  const summaryCards: SummaryCard[] = [
    {
      id: 'clock',
      titleKey: 'dashboard.card.currentTime',
      value: '', // Empty - handled by HijriClock component
      icon: 'clock',
      color: 'blue'
    },
    {
      id: 'active-alerts',
      titleKey: 'dashboard.card.activeTripsWithAlerts',
      value: data?.statistics.activeTripsWithAlerts || 0,
      icon: 'bell-orange',
      color: 'orange'
    },
    {
      id: 'total-alerts',
      titleKey: 'dashboard.card.totalActiveTripsWithAlerts',
      value: data?.statistics.totalActiveTripsWithAlerts || 0,
      icon: 'bell-gray',
      color: 'gray'
    },
    {
      id: 'total-active',
      titleKey: 'dashboard.card.totalActiveTrips',
      value: data?.statistics.totalActiveTrips || 0,
      icon: 'check',
      color: 'green'
    },
    {
      id: 'total-closed',
      titleKey: 'dashboard.card.totalClosedTrips',
      value: data?.statistics.totalClosedTrips || 0,
      icon: 'prohibition',
      color: 'red'
    }
  ];

  // Additional translation keys needed for dashboard cards:
  // translations/en.ts
  const dashboardTranslations = {
    'dashboard.card.currentTime': 'Current Time',
    'dashboard.card.activeTripsWithAlerts': 'Active Trips with Alerts',
    'dashboard.card.totalActiveTripsWithAlerts': 'Total Active Trips with Alerts',
    'dashboard.card.totalActiveTrips': 'Total Active Trips',
    'dashboard.card.totalClosedTrips': 'Total Closed Trips'
  };

  // translations/ar.ts
  const dashboardTranslationsAr = {
    'dashboard.card.currentTime': 'الوقت الحالي',
    'dashboard.card.activeTripsWithAlerts': 'الرحلات النشطة مع التنبيهات',
    'dashboard.card.totalActiveTripsWithAlerts': 'إجمالي الرحلات النشطة مع التنبيهات',
    'dashboard.card.totalActiveTrips': 'إجمالي الرحلات النشطة',
    'dashboard.card.totalClosedTrips': 'إجمالي الرحلات المغلقة'
  };

  return (
    <div className="page-container">
      {/* Reusable Summary Cards with Clock */}
      <div className="summary-section">
        <HijriClock className="hijri-clock-card" />
        <DashboardSummaryCards
          cards={summaryCards.slice(1)} // Exclude clock card
          layout="horizontal"
        />
      </div>

      {/* Reusable Data Table */}
      <DataTable
        data={data?.trips || []}
        columns={myAssignedPortsColumns}
        exportable={true}
        exportFormats={['xlsx']}
        exportFileNameKey="table.export.filename.myAssignedPorts"
        rowStatusField="alertStatus"
        statusConfig={{
          warning: { color: 'yellow', icon: <WarningIcon />, highlight: false },
          critical: { color: 'red', icon: <CriticalIcon />, highlight: true }
        }}
        onRowClick={(trip) => navigate(`/trip-details/${trip.tripId}`)}
        searchable={true}
        filterable={true}
        pageSize={20}
        emptyMessageKey="table.empty.noData"
        // Master-Detail Configuration
        expandable={true}
        expandedRowRender={(trip) => <TripDetailPanel trip={trip} />}
        expandRowByClick={false}
        expandIconColumnIndex={0}
      />
    </div>
  );
};

// Location Monitor Page (Different data, same components)
const LocationMonitorPage: React.FC = () => {
  // Uses same DashboardSummaryCards and DataTable components
  // But with different data source and column configuration
  const summaryCards = [/* location-specific cards */];
  const columns = locationMonitorColumns;

  return (
    <div className="page-container">
      <DashboardSummaryCards cards={summaryCards} layout="grid-2x2" />
      <DataTable
        data={locationData}
        columns={columns}
        // Different configuration for location monitoring
        autoRefresh={30000} // 30 seconds
        realTimeUpdates={true}
        // Master-Detail for real-time trip monitoring
        expandable={true}
        expandedRowRender={(trip) => <LiveTripDetailPanel trip={trip} />}
        expandRowByClick={true} // Quick expansion for monitoring
      />
    </div>
  );
};

### Translation Keys for Master-Detail Feature

```typescript
// Additional translation keys for master-detail functionality
// Add to translations/en.ts and translations/ar.ts

const masterDetailTranslations = {
  // Expand/Collapse Actions
  'table.action.expand': 'Expand Details',
  'table.action.collapse': 'Collapse Details',
  'table.action.expandAll': 'Expand All',
  'table.action.collapseAll': 'Collapse All',

  // Detail Panel Headers
  'detail.panel.shipmentDetails': 'Shipment Details',
  'detail.panel.trackingStatus': 'Tracking Status',
  'detail.panel.alertHistory': 'Alert History',
  'detail.panel.actions': 'Actions',
  'detail.panel.driverInfo': 'Driver Information',
  'detail.panel.vehicleInfo': 'Vehicle Information',
  'detail.panel.routeProgress': 'Route Progress',
  'detail.panel.compliance': 'Compliance Status',

  // Detail Actions
  'detail.action.viewFull': 'View Full Details',
  'detail.action.generateReport': 'Generate Report',
  'detail.action.contactDriver': 'Contact Driver',
  'detail.action.assignInvestigator': 'Assign Investigator',
  'detail.action.escalate': 'Escalate',
  'detail.action.markResolved': 'Mark Resolved',

  // Loading States
  'detail.loading': 'Loading details...',
  'detail.error': 'Failed to load details',
  'detail.retry': 'Retry',

  // Accessibility
  'table.row.expandDetails': 'Expand details for {rowTitle}',
  'table.row.collapseDetails': 'Collapse details for {rowTitle}',
  'detail.region.label': 'Detailed information for {rowTitle}'
};

// Arabic translations
const masterDetailTranslationsAr = {
  'table.action.expand': 'توسيع التفاصيل',
  'table.action.collapse': 'طي التفاصيل',
  'table.action.expandAll': 'توسيع الكل',
  'table.action.collapseAll': 'طي الكل',

  'detail.panel.shipmentDetails': 'تفاصيل الشحنة',
  'detail.panel.trackingStatus': 'حالة التتبع',
  'detail.panel.alertHistory': 'تاريخ التنبيهات',
  'detail.panel.actions': 'الإجراءات',
  'detail.panel.driverInfo': 'معلومات السائق',
  'detail.panel.vehicleInfo': 'معلومات المركبة',
  'detail.panel.routeProgress': 'تقدم الطريق',
  'detail.panel.compliance': 'حالة الامتثال',

  'detail.action.viewFull': 'عرض التفاصيل الكاملة',
  'detail.action.generateReport': 'إنشاء تقرير',
  'detail.action.contactDriver': 'الاتصال بالسائق',
  'detail.action.assignInvestigator': 'تعيين محقق',
  'detail.action.escalate': 'تصعيد',
  'detail.action.markResolved': 'وضع علامة كمحلول',

  'detail.loading': 'جاري تحميل التفاصيل...',
  'detail.error': 'فشل في تحميل التفاصيل',
  'detail.retry': 'إعادة المحاولة',

  'table.row.expandDetails': 'توسيع تفاصيل {rowTitle}',
  'table.row.collapseDetails': 'طي تفاصيل {rowTitle}',
  'detail.region.label': 'معلومات مفصلة لـ {rowTitle}'
};
```

### Visual Indicators

- **Yellow warning circles**: For trips with alerts
- **Red row highlighting**: For critical status trips
- **Icon indicators**: Clock, bell notifications, checkmarks, prohibition signs
- **Bilingual headers**: English/Arabic column headers
- **Export functionality**: XLS download capability
