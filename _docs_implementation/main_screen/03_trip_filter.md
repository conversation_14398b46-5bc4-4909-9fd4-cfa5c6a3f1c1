Below is a summarized description of each accordion section in the `trip_filter_form`, listing the filters included in each group without any code or JSON.

### 1. Entry Port
- Filters trips by entry port.
- **Filters**:
  - Select All (checkbox)
  - Entry Port Checkboxes (40 ports, e.g., Jeddah Islamic Seaport, King Fahad Intl Airport, Batha Customs Border, etc.)

### 2. Exit Port
- Filters trips by exit port.
- **Filters**:
  - Select All (checkbox)
  - Exit Port Checkboxes (40 ports, identical to Entry Port list)

### 3. Alerts
- Filters trips by alert status and types, grouped by priority.
- **Filters**:
  - Alert Status: Acknowledge, Not Acknowledge (checkboxes)
  - High Priority Alerts: Select All (checkbox), Alert Types (16, e.g., Tracker Tamper, Lock Open, Over Speeding, etc.)
  - Medium Priority Alerts: Select All (checkbox, no specific alert types listed)
  - Low Priority Alerts: Select All (checkbox, no specific alert types listed)

### 4. Truck Info
- Filters trips by truck and driver details.
- **Filters**:
  - Transit No (text input)
  - Transit Sequence No (text input)
  - Driver Name (text input)
  - Driver Nationality (dropdown, extensive country list in Arabic)
  - Truck Nationality (text input)
  - Charger Status (dropdown: Plugged In, Unplugged)
  - Plate No (text input)
  - Tracker No (text input)
  - Trip Code (text input)
  - Trip Priority: High, Medium, Low (checkboxes)
  - Trip Status: Active, Ended (checkboxes)
  - Trip Location: Target Onroute, Target in Exit Border, Target in Entry Border (checkboxes)

### 5. Dates
- Filters trips by transit and entry dates.
- **Filters**:
  - Transit Date Type: Gregorian (checkbox)
  - Transit Date (date input)
  - Entry Date From (date input)
  - Entry Date To (date input)

### 6. Order By
- Specifies sorting order and field for results.
- **Filters**:
  - Order Direction: Ascending, Descending (radio buttons)
  - Order By Field: Trip Code, Entry Port, Exit Port, Transit No, Transit Date, Entry Date, Exit Date, Created Date (radio buttons)

### 7. Shipment Description
- Filters trips by shipment type.
- **Filters**:
  - Shipment Description Checkboxes (25, e.g., Clothing and Shoes, Cosmetics, Spare Parts, Live Animals, Medicines, etc.)

### 8. Transit Type
- Filters trips by transit type.
- **Filters**:
  - Transit Type Checkboxes (9, e.g., Loaded Transit, Statistical Declaration, Import Customs Declaration, Passengers Transit, Gulf Destination, etc.)

### 9. Date Time
- Filters trips by creation date range.
- **Filters**:
  - Created At Start (datetime input)
  - Created At End (datetime input)

### 10. Form Buttons
- Controls form submission and reset.
- **Filters**:
  - Search (submit button)
  - Reset (button)