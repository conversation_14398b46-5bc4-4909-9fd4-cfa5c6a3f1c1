# Google Maps Integration Guide

## Overview

This guide covers the implementation of Google Maps in React applications, including API setup, marker management, interactive features, and best practices for location-based applications.

## Prerequisites

### Google Cloud Platform Setup

1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable billing for the project

2. **Enable Required APIs**
   - Navigate to "APIs & Services" > "Library"
   - Enable the following APIs:
     - Maps JavaScript API
     - Places API (optional, for place search)
     - Geocoding API (optional, for address conversion)

3. **Create API Key**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the generated API key
   - Restrict the key to your domain for security

### Environment Configuration

Create or update your `.env.local` file:

```bash
# Google Maps API Key
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

**Security Note**: The `NEXT_PUBLIC_` prefix makes this available to the browser. Ensure you restrict your API key to your domain.

## Basic Implementation

### 1. Component Structure

```typescript
// Basic map component structure
export default function MapDemo() {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  
  // Implementation details...
}
```

### 2. API Loading Pattern (Singleton Utility)

**Problem**: Multiple components loading Google Maps API causes console errors.

**Solution**: Use a singleton utility to manage API loading globally.

```typescript
// utils/googleMaps.ts - Singleton API loader
let isLoading = false;
let isLoaded = false;
let loadPromise: Promise<void> | null = null;

export function loadGoogleMapsAPI(): Promise<void> {
  // Return existing promise if already loading
  if (loadPromise) {
    return loadPromise;
  }

  // Return resolved promise if already loaded
  if (isLoaded && window.google && window.google.maps) {
    return Promise.resolve();
  }

  // Check for existing script to prevent duplicates
  const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
  if (existingScript && !isLoading) {
    loadPromise = new Promise((resolve, reject) => {
      existingScript.addEventListener('load', () => {
        isLoaded = true;
        resolve();
      });
      existingScript.addEventListener('error', () => {
        reject(new Error('Failed to load Google Maps API'));
      });
    });
    return loadPromise;
  }

  // Create new loading promise
  loadPromise = new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.defer = true;
    script.onload = () => {
      isLoaded = true;
      resolve();
    };
    script.onerror = () => {
      reject(new Error('Failed to load Google Maps API'));
    };
    document.head.appendChild(script);
  });

  return loadPromise;
}
```

**Component Usage**:
```typescript
// app/map-demo/page.tsx
import { loadGoogleMapsAPI } from '../../utils/googleMaps';

useEffect(() => {
  loadGoogleMapsAPI()
    .then(() => {
      setIsLoaded(true);
      setError(null);
    })
    .catch((err) => {
      setError(err.message);
    });
}, []);
```

### 3. Map Initialization

```typescript
// Initialize map instance
useEffect(() => {
  if (!isLoaded || !mapRef.current) return;

  const mapInstance = new google.maps.Map(mapRef.current, {
    center: { lat: 24.7136, lng: 46.6753 }, // Riyadh center
    zoom: 11,
    styles: [
      {
        featureType: 'poi',
        elementType: 'labels',
        stylers: [{ visibility: 'off' }]
      }
    ]
  });

  setMap(mapInstance);
}, [isLoaded]);
```

## Marker Implementation

### 1. Custom Marker Icons

```typescript
// SVG-based custom marker icon
const createPoliceIcon = () => ({
  url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="15" fill="#1e40af" stroke="white" stroke-width="2"/>
      <path d="M16 8L18.5 13H23L19.5 16.5L21 22L16 19L11 22L12.5 16.5L9 13H13.5L16 8Z" fill="white"/>
    </svg>
  `),
  scaledSize: new google.maps.Size(32, 32),
  anchor: new google.maps.Point(16, 32)
});
```

### 2. Marker Management

```typescript
// Add markers with event listeners
useEffect(() => {
  if (!map) return;

  // Clear existing markers
  markers.forEach(marker => marker.setMap(null));

  const newMarkers = locations.map(location => {
    const marker = new google.maps.Marker({
      position: { lat: location.lat, lng: location.lng },
      map: map,
      title: location.name,
      icon: createPoliceIcon()
    });

    // Add event listeners
    marker.addListener('mouseover', () => {
      setSelectedLocation(location);
    });

    marker.addListener('mouseout', () => {
      setSelectedLocation(null);
    });

    marker.addListener('click', () => {
      map.setCenter({ lat: location.lat, lng: location.lng });
      map.setZoom(15);
    });

    return marker;
  });

  setMarkers(newMarkers);

  // Cleanup function
  return () => {
    newMarkers.forEach(marker => marker.setMap(null));
  };
}, [map, locations]);
```

## Interactive Features

### 1. Hover Information Panel

```typescript
// Hover info panel component
{selectedLocation && (
  <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg border p-4 max-w-sm animate-in slide-in-from-left-2 duration-200">
    <div className="flex items-start space-x-3">
      <div className="bg-blue-100 p-2 rounded-lg">
        <Shield className="w-5 h-5 text-blue-600" />
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="text-sm font-semibold text-gray-900 mb-1">
          {selectedLocation.name}
        </h3>
        <p className="text-xs text-gray-600 mb-2">
          {selectedLocation.address}
        </p>
        {/* Additional details */}
      </div>
    </div>
  </div>
)}
```

### 2. Map Controls

```typescript
// Reset view functionality
const resetMapView = () => {
  if (map) {
    map.setCenter({ lat: 24.7136, lng: 46.6753 });
    map.setZoom(11);
    setSelectedLocation(null);
  }
};
```

## Data Structure

### Location Data Model

```typescript
interface Location {
  id: string;
  name: string;
  nameAr: string;          // Arabic name for i18n
  lat: number;
  lng: number;
  address: string;
  addressAr: string;       // Arabic address
  phone: string;
  category: string;        // e.g., 'police', 'hospital', 'school'
  status: 'active' | 'inactive';
  metadata?: {
    officers?: number;
    capacity?: number;
    hours?: string;
    [key: string]: any;
  };
}
```

### Sample Data (Riyadh Police Stations)

```typescript
const policeStations: Location[] = [
  {
    id: '1',
    name: 'King Fahd District Police Station',
    nameAr: 'مركز شرطة حي الملك فهد',
    lat: 24.7136,
    lng: 46.6753,
    address: 'King Fahd Road, Riyadh 12271',
    addressAr: 'طريق الملك فهد، الرياض 12271',
    phone: '+966-11-401-1234',
    category: 'police',
    status: 'active',
    metadata: {
      officers: 45,
      hours: '24/7'
    }
  }
  // ... more locations
];
```

## Internationalization Support

### 1. Language-Aware Display

```typescript
// Display names based on current language
const getLocationName = (location: Location, language: string) => {
  return language === 'ar' ? location.nameAr : location.name;
};

const getLocationAddress = (location: Location, language: string) => {
  return language === 'ar' ? location.addressAr : location.address;
};
```

### 2. RTL Map Considerations

```typescript
// Map configuration for RTL languages
const mapOptions = {
  center: { lat: 24.7136, lng: 46.6753 },
  zoom: 11,
  // RTL-specific configurations
  gestureHandling: 'cooperative',
  zoomControl: true,
  zoomControlOptions: {
    position: language === 'ar' 
      ? google.maps.ControlPosition.LEFT_BOTTOM 
      : google.maps.ControlPosition.RIGHT_BOTTOM
  }
};
```

## Performance Optimization

### 1. Marker Clustering

```typescript
// For large numbers of markers, implement clustering
import { MarkerClusterer } from '@googlemaps/markerclusterer';

const clusterer = new MarkerClusterer({
  map,
  markers: newMarkers,
  algorithm: new SuperClusterAlgorithm({
    radius: 100,
    maxZoom: 15
  })
});
```

### 2. Lazy Loading

```typescript
// Load map only when component is visible
const [isVisible, setIsVisible] = useState(false);

useEffect(() => {
  const observer = new IntersectionObserver(
    ([entry]) => {
      if (entry.isIntersecting) {
        setIsVisible(true);
        observer.disconnect();
      }
    },
    { threshold: 0.1 }
  );

  if (mapRef.current) {
    observer.observe(mapRef.current);
  }

  return () => observer.disconnect();
}, []);
```

## Error Handling

### 1. API Loading Errors

```typescript
const [error, setError] = useState<string | null>(null);

// Handle API loading failures
script.onerror = () => {
  setError('Failed to load Google Maps API. Please check your API key.');
};

// Display error state
if (error) {
  return (
    <div className="bg-white rounded-lg border p-8 text-center">
      <MapPin className="w-12 h-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">Map Loading Error</h3>
      <p className="text-gray-500">{error}</p>
    </div>
  );
}
```

### 2. Geolocation Errors

```typescript
// Handle geolocation failures gracefully
const getCurrentLocation = () => {
  if (!navigator.geolocation) {
    setError('Geolocation is not supported by this browser.');
    return;
  }

  navigator.geolocation.getCurrentPosition(
    (position) => {
      const { latitude, longitude } = position.coords;
      map?.setCenter({ lat: latitude, lng: longitude });
    },
    (error) => {
      console.error('Geolocation error:', error);
      // Fallback to default location
      map?.setCenter({ lat: 24.7136, lng: 46.6753 });
    }
  );
};
```

## Security Best Practices

### 1. API Key Restrictions

- **HTTP Referrers**: Restrict to your domain(s)
- **IP Addresses**: Restrict to your server IPs if applicable
- **API Restrictions**: Enable only required APIs

### 2. Environment Variables

```bash
# Production environment
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=production_key_here

# Development environment
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=development_key_here
```

### 3. Rate Limiting

```typescript
// Implement request throttling for API calls
const throttle = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  let lastExecTime = 0;
  
  return (...args: any[]) => {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};
```

## Testing Strategies

### 1. Unit Testing

```typescript
// Mock Google Maps for testing
const mockGoogleMaps = {
  Map: jest.fn(),
  Marker: jest.fn(),
  InfoWindow: jest.fn()
};

(global as any).google = {
  maps: mockGoogleMaps
};
```

### 2. Integration Testing

```typescript
// Test marker interactions
test('should display location details on marker hover', async () => {
  render(<MapDemo />);
  
  // Wait for map to load
  await waitFor(() => {
    expect(screen.getByText('Loading Google Maps...')).not.toBeInTheDocument();
  });
  
  // Simulate marker hover
  const marker = screen.getByRole('button', { name: /police station/i });
  fireEvent.mouseOver(marker);
  
  // Check if details panel appears
  expect(screen.getByText('King Fahd District Police Station')).toBeInTheDocument();
});
```

## Deployment Considerations

### 1. Build Configuration

```javascript
// next.config.js
module.exports = {
  env: {
    NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  },
  // Other configurations...
};
```

### 2. Performance Monitoring

```typescript
// Track map performance metrics
const trackMapPerformance = () => {
  const startTime = performance.now();
  
  map?.addListener('idle', () => {
    const loadTime = performance.now() - startTime;
    console.log(`Map loaded in ${loadTime}ms`);
    
    // Send to analytics
    gtag('event', 'map_load_time', {
      value: Math.round(loadTime),
      custom_parameter: 'map_performance'
    });
  });
};
```

## API Layer Separation Best Practices

### Data Layer Architecture

The TTS Template implements a clean separation between data sources and UI components, following these principles:

#### 1. API Layer Structure
```
api/
├── police-stations.ts    # Police stations API functions
├── shipment-alerts.ts    # Shipment alerts API functions
└── [other-apis].ts       # Additional API modules

data/
├── police-stations.json  # Static police stations data
├── shipment-alerts.json  # Static shipment alerts data
└── [other-data].json     # Additional static data files
```

#### 2. Separation of Concerns

**Data Layer (`/data/`):**
- Static JSON files with structured data
- Realistic data models for development and testing
- Easy to maintain and update
- Version controllable

**API Layer (`/api/`):**
- TypeScript interfaces for type safety
- HTTP-like function signatures
- Simulated API delays for realistic behavior
- Easy migration path to real backends

**Component Layer (`/app/`):**
- UI components consume API functions
- No direct data imports
- Proper loading and error states
- Separation of presentation and data logic

#### 3. Migration Strategy

**Current Implementation (Static Data):**
```typescript
// api/police-stations.ts
import policeStationsData from '../data/police-stations.json';

export async function getPoliceStations(): Promise<PoliceStationsResponse> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  return {
    metadata: policeStationsData.metadata,
    stations: policeStationsData.stations
  };
}
```

**Future Implementation (Real Backend):**
```typescript
// api/police-stations.ts
export async function getPoliceStations(): Promise<PoliceStationsResponse> {
  const response = await fetch(`${API_CONFIG.BASE_URL}/police-stations`, {
    method: 'GET',
    headers: API_CONFIG.getHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}
```

#### 4. Component Integration Pattern

**Clean Component Implementation:**
```typescript
// app/map-demo/page.tsx
import { getPoliceStations, PoliceStation } from '../../api/police-stations';

export default function MapDemo() {
  const [stations, setStations] = useState<PoliceStation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStations = async () => {
      try {
        setLoading(true);
        const response = await getPoliceStations({ status: 'active' });
        setStations(response.stations);
      } catch (err) {
        setError('Failed to load police stations data');
      } finally {
        setLoading(false);
      }
    };

    fetchStations();
  }, []);

  // Component renders based on state...
}
```

### Benefits of This Architecture

#### 1. **Maintainability**
- Clear separation of data, API, and UI layers
- Easy to update data without touching components
- Centralized API logic for consistent behavior

#### 2. **Testability**
- Mock API functions for unit testing
- Test components independently of data sources
- Validate data transformations in isolation

#### 3. **Scalability**
- Add new API endpoints without component changes
- Support multiple data sources (cache, API, local storage)
- Easy to implement features like offline support

#### 4. **Type Safety**
- TypeScript interfaces ensure data consistency
- Compile-time error checking for data structure changes
- IntelliSense support for better developer experience

#### 5. **Migration Readiness**
- Seamless transition from static to dynamic data
- No component changes required during migration
- Gradual migration possible (endpoint by endpoint)

### Implementation Guidelines

#### 1. **API Function Design**
```typescript
// Good: Consistent function signatures
export async function getPoliceStations(filters?: StationFilters): Promise<PoliceStationsResponse>
export async function getPoliceStationById(id: string): Promise<PoliceStation | null>
export async function getNearbyStations(lat: number, lng: number, radius?: number): Promise<PoliceStation[]>

// Good: Proper error handling
try {
  const response = await getPoliceStations();
  return response.stations;
} catch (error) {
  console.error('Failed to fetch stations:', error);
  throw new Error('Unable to load police stations');
}
```

#### 2. **Data Structure Design**
```typescript
// Good: Hierarchical, well-structured data
interface PoliceStation {
  id: string;
  name: string;
  nameAr: string;
  coordinates: Coordinates;
  address: Address;
  addressAr: Address;
  contact: Contact;
  operational: Operational;
  services: string[];
}

// Good: Separate interfaces for complex objects
interface Address {
  street: string;
  district: string;
  city: string;
  postalCode: string;
  full: string;
}
```

#### 3. **Component State Management**
```typescript
// Good: Proper loading states
const [data, setData] = useState<DataType[]>([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState<string | null>(null);

// Good: Error boundaries
if (error) {
  return <ErrorComponent message={error} />;
}

if (loading) {
  return <LoadingComponent />;
}
```

### Testing Strategies

#### 1. **API Layer Testing**
```typescript
// Mock API responses for testing
jest.mock('../api/police-stations', () => ({
  getPoliceStations: jest.fn().mockResolvedValue({
    metadata: { totalStations: 2 },
    stations: [mockStation1, mockStation2]
  })
}));
```

#### 2. **Component Testing**
```typescript
// Test component behavior with different data states
test('displays loading state initially', () => {
  render(<MapDemo />);
  expect(screen.getByText('Loading police stations data...')).toBeInTheDocument();
});

test('displays error state on API failure', async () => {
  (getPoliceStations as jest.Mock).mockRejectedValue(new Error('API Error'));
  render(<MapDemo />);
  await waitFor(() => {
    expect(screen.getByText('Failed to load police stations data')).toBeInTheDocument();
  });
});
```

### Performance Considerations

#### 1. **Data Caching**
```typescript
// Implement caching for frequently accessed data
const cache = new Map<string, { data: any; timestamp: number }>();

export async function getCachedPoliceStations(): Promise<PoliceStationsResponse> {
  const cacheKey = 'police-stations';
  const cached = cache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) { // 5 minutes
    return cached.data;
  }

  const data = await getPoliceStations();
  cache.set(cacheKey, { data, timestamp: Date.now() });
  return data;
}
```

#### 2. **Lazy Loading**
```typescript
// Load data only when component is visible
const [isVisible, setIsVisible] = useState(false);

useEffect(() => {
  const observer = new IntersectionObserver(([entry]) => {
    if (entry.isIntersecting) {
      setIsVisible(true);
      observer.disconnect();
    }
  });

  if (mapRef.current) {
    observer.observe(mapRef.current);
  }

  return () => observer.disconnect();
}, []);

useEffect(() => {
  if (isVisible) {
    fetchStations();
  }
}, [isVisible]);
```

This architecture provides a solid foundation for scalable, maintainable applications with clear separation of concerns and easy migration paths to production backends.
