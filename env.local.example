# Google Maps API Configuration
# Get your API key from: https://console.cloud.google.com/apis/credentials
# Make sure to enable Maps JavaScript API and restrict the key to your domain
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyDk9C1Ht7B8h33ptLRLhWu-JtStV0GfzxY

# Example:
# NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Security Notes:
# 1. Never commit your actual API key to version control
# 2. Restrict your API key to your domain in Google Cloud Console
# 3. Enable only the APIs you need (Maps JavaScript API, Places API, etc.)
# 4. Monitor your API usage to avoid unexpected charges
