/**
 * Google Maps API Loader Utility
 *
 * This utility provides a singleton pattern for loading the Google Maps API
 * to prevent multiple script inclusions and console errors.
 */

import React from "react";

// Global state to track loading status
let isLoading = false;
let isLoaded = false;
let loadPromise: Promise<void> | null = null;

/**
 * Load Google Maps API with singleton pattern
 *
 * @returns Promise that resolves when Google Maps API is loaded
 */
export function loadGoogleMapsAPI(): Promise<void> {
  console.log("[GoogleMapsLoader] Starting loadGoogleMapsAPI");

  // Return existing promise if already loading
  if (loadPromise) {
    console.log(
      "[GoogleMapsLoader] Already loading, returning existing promise"
    );
    return loadPromise;
  }

  // Return resolved promise if already loaded
  if (isLoaded && window.google && window.google.maps) {
    console.log(
      "[GoogleMapsLoader] Already loaded, returning resolved promise"
    );
    return Promise.resolve();
  }

  // Check if script already exists in DOM
  const existingScript = document.querySelector(
    'script[src*="maps.googleapis.com"]'
  ) as HTMLScriptElement;

  if (existingScript) {
    console.log("[GoogleMapsLoader] Found existing Google Maps script");
    if (window.google?.maps) {
      console.log("[GoogleMapsLoader] Google Maps API already loaded");
      isLoaded = true;
      return Promise.resolve();
    }

    if (!isLoading) {
      console.log("[GoogleMapsLoader] Waiting for existing script to load");
      loadPromise = new Promise((resolve, reject) => {
        existingScript.addEventListener("load", () => {
          console.log("[GoogleMapsLoader] Existing script loaded successfully");
          isLoaded = true;
          isLoading = false;
          resolve();
        });
        existingScript.addEventListener("error", () => {
          console.log("[GoogleMapsLoader] Existing script failed to load");
          isLoading = false;
          reject(new Error("Failed to load Google Maps API"));
        });
      });
      return loadPromise;
    }
  }

  // Create new loading promise
  loadPromise = new Promise((resolve, reject) => {
    // Check if already loaded (race condition protection)
    if (window.google?.maps) {
      console.log(
        "[GoogleMapsLoader] Google Maps API already loaded (race condition check)"
      );
      isLoaded = true;
      resolve();
      return;
    }

    console.log("[GoogleMapsLoader] Creating new script for Google Maps API");
    isLoading = true;

    const script = document.createElement("script");
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      isLoading = false;
      reject(new Error("Google Maps API key is not configured"));
      return;
    }

    console.log(
      "[GoogleMapsLoader] Creating script with API key:",
      apiKey ? "Present" : "Missing"
    );

    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log("[GoogleMapsLoader] Script loaded successfully");
      isLoaded = true;
      isLoading = false;
      resolve();
    };

    script.onerror = () => {
      isLoading = false;
      reject(new Error("Failed to load Google Maps API"));
    };

    document.head.appendChild(script);
  });

  return loadPromise;
}

/**
 * Check if Google Maps API is loaded
 *
 * @returns boolean indicating if Google Maps API is available
 */
export function isGoogleMapsLoaded(): boolean {
  return !!window.google?.maps;
}

/**
 * Reset the loading state (useful for testing)
 */
export function resetGoogleMapsLoader(): void {
  isLoading = false;
  isLoaded = false;
  loadPromise = null;
}

/**
 * React hook for loading Google Maps API
 *
 * @returns Object with loading state and error
 */
export function useGoogleMaps() {
  const [loading, setLoading] = React.useState(!isGoogleMapsLoaded());
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (isGoogleMapsLoaded()) {
      setLoading(false);
      return;
    }

    loadGoogleMapsAPI()
      .then(() => {
        setLoading(false);
        setError(null);
      })
      .catch((err) => {
        setLoading(false);
        setError(err.message);
      });
  }, []);

  return { loading, error, isLoaded: !loading && !error };
}

// Type declarations for Google Maps (basic)
declare global {
  interface Window {
    google: {
      maps: {
        Map: any;
        Marker: any;
        InfoWindow: any;
        Size: any;
        Point: any;
        LatLng: any;
        LatLngBounds: any;
        // Add more as needed
      };
    };
  }
}
