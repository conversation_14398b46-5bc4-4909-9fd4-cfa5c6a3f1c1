/**
 * User Data Management
 * Contains user data and provides utility functions
 */

import { User } from "@/types/auth";

export const MOCK_USERS: User[] = [
  {
    id: "1",
    username: "admin",
    password: "admin123",
    employeeName: "System Administrator",
    employeeNameArabic: "مدير النظام",
    email: "<EMAIL>",
    role: "admin",
    organization: "zatca",
    status: "active",
    permissions: {
      canViewTrips: true,
      canManageTrips: true,
      canViewAlerts: true,
      canManageAlerts: true,
      canManageUsers: true,
      canAssignPorts: true,
      canViewReports: true,
      canExportData: true,
    },
    profile: {
      department: "System Administration",
      phoneNumber: "+966501234567",
      preferredLanguage: "ar",
      timezone: "Asia/Riyadh",
    },
  },
  {
    id: "2",
    username: "operator",
    password: "operator123",
    employeeName: "System Operator",
    employeeNameArabic: "مشغل النظام",
    email: "<EMAIL>",
    role: "operator",
    organization: "zatca",
    status: "active",
    permissions: {
      canViewTrips: true,
      canManageTrips: false,
      canViewAlerts: true,
      canManageAlerts: true,
      canManageUsers: false,
      canAssignPorts: false,
      canViewReports: true,
      canExportData: false,
    },
    profile: {
      department: "Operations",
      phoneNumber: "+966507654321",
      preferredLanguage: "ar",
      timezone: "Asia/Riyadh",
    },
  },
  {
    id: "3",
    username: "supervisor",
    password: "supervisor123",
    employeeName: "Operations Supervisor",
    employeeNameArabic: "مشرف العمليات",
    email: "<EMAIL>",
    role: "supervisor",
    organization: "zatca",
    status: "active",
    permissions: {
      canViewTrips: true,
      canManageTrips: true,
      canViewAlerts: true,
      canManageAlerts: true,
      canManageUsers: false,
      canAssignPorts: true,
      canViewReports: true,
      canExportData: true,
    },
    profile: {
      department: "Operations Management",
      phoneNumber: "+966509876543",
      preferredLanguage: "ar",
      timezone: "Asia/Riyadh",
    },
  },
];

export const getActiveUsers = (): User[] => {
  return MOCK_USERS.filter((user) => user.status === "active");
};

export const findUserByUsername = (username: string): User | undefined => {
  return MOCK_USERS.find(
    (user) => user.username === username && user.status === "active"
  );
};

export const findUserById = (id: string): User | undefined => {
  return MOCK_USERS.find((user) => user.id === id && user.status === "active");
};

export const validateUserCredentials = (
  username: string,
  password: string
): User | undefined => {
  return MOCK_USERS.find(
    (user) =>
      user.username === username &&
      user.password === password &&
      user.status === "active"
  );
};
