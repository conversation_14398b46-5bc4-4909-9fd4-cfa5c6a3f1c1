/**
 * Authentication Types
 * TypeScript interfaces and types for authentication and user management
 */

export interface UserPermissions {
  canViewTrips: boolean;
  canManageTrips: boolean;
  canViewAlerts: boolean;
  canManageAlerts: boolean;
  canManageUsers: boolean;
  canAssignPorts: boolean;
  canViewReports: boolean;
  canExportData: boolean;
}

export interface UserProfile {
  department: string;
  phoneNumber: string;
  preferredLanguage: "ar" | "en";
  timezone: string;
}

export type UserRole = "admin" | "supervisor" | "operator" | "viewer";
export type UserStatus = "active" | "inactive" | "suspended" | "pending";
export type Organization = "zatca" | "customs" | "technical_united";

export interface User {
  id: string;
  username: string;
  password: string;
  employeeName: string;
  employeeNameArabic: string;
  email: string;
  role: UserRole;
  organization: Organization;
  status: UserStatus;
  permissions: UserPermissions;
  profile: UserProfile;
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  success: boolean;
  user?: Omit<User, "password">;
  message?: string;
  errorCode?: string;
}

export interface UserSession {
  user: Omit<User, "password">;
  timestamp: number;
  rememberMe: boolean;
  expiresAt: number;
}

export interface AuthContextType {
  user: Omit<User, "password"> | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (user: Omit<User, "password">) => void;
  logout: () => void;
  refreshUser: () => void;
}
