export interface TripDriver {
  driverId: string;
  driverName: string;
  driverPassportNumber: string;
  driverNationality: string;
  driverContactNo?: string;
}

export interface TripVehicle {
  vehicleId: string;
  vehiclePlateNumber: string;
  trackerNo: string;
  model?: string;
  color?: string;
  type?: string;
  plateCountry?: string;
}

export interface TripRoute {
  routeId: string;
  routeName: string;
  entryPort: string;
  exitPort: string;
}

export interface TripShipment {
  shipmentId: string;
  shipmentDescription: string;
  ownerDescription?: string;
  shipmentDescriptionArabic?: string;
}

export interface TripTracking {
  currentLocation: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
  completeDistance: number;
  remainingDistance: number;
  estimatedArrival: string | null;
  elocks: string;
}

export interface TripCompliance {
  securityNotes: string;
  customsStatus: string;
  documentStatus: string;
}

export interface Trip {
  id: number;
  transitNumber: number;
  description: string;
  entry: string;
  lastSeen: string;
  tracker: string;
  driver: string;
  vehicle: string;
  alerts: string;
  status: ("active" | "charging" | "offline")[];
  tripId: string;
  tripStatus: 'pending' | 'activated' | 'ended' | 'cancelled';
  transitType: string;
  creationDate: string;
  activationDate: string | null;
  completionDate: string | null;
  transitSeqNo?: string;
  declarationDate?: string;
  startingDate?: string;
  expectedArrivalDate?: string;
  endDate?: string;
  driver_details: TripDriver;
  vehicle_details: TripVehicle;
  route: TripRoute;
  shipment: TripShipment;
  tracking: TripTracking;
  compliance: TripCompliance;
}