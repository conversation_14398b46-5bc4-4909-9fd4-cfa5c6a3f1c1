'use client';
import React, { useState } from 'react';

interface OrderByFilterProps {
  value: {
    direction: 'asc' | 'desc';
    field: string;
  };
  onChange: (value: { direction: 'asc' | 'desc'; field: string }) => void;
}

export default function OrderByFilter({
  value = { direction: 'asc', field: 'Created Date' },
  onChange = () => {},
}: OrderByFilterProps) {
  const [expanded, setExpanded] = useState(false);

  const update = (key: 'direction' | 'field', v: string) => {
    onChange({ ...value, [key]: v });
  };

  const fields = [
    'Trip Code',
    'Entry Port',
    'Exit Port',
    'Transit No',
    'Transit Date',
    'Entry Date',
    'Exit Date',
    'Created Date',
  ];

  return (
    <div className="border border-gray-200 rounded mb-4">
      <button
        type="button"
        onClick={() => setExpanded((v) => !v)}
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
      >
        <span className="font-medium">Order By</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${
            expanded ? '' : 'rotate-180'
          }`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {expanded && (
        <div className="px-4 py-3 space-y-4">
          {/* Direction */}
          <div>
            <div className="font-medium text-sm mb-1">Direction</div>
            <div className="flex space-x-4">
              {[
                { label: 'Ascending', value: 'asc' },
                { label: 'Descending', value: 'desc' },
              ].map((opt) => (
                <label key={opt.value} className="flex items-center text-sm">
                  <input
                    type="radio"
                    name="order-direction"
                    className="form-radio h-4 w-4 text-blue-600"
                    checked={value.direction === opt.value}
                    onChange={() => update('direction', opt.value)}
                  />
                  <span className="ml-2">{opt.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Field */}
          <div>
            <div className="font-medium text-sm mb-1">Field</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
              {fields.map((f) => (
                <label key={f} className="flex items-center text-sm">
                  <input
                    type="radio"
                    name="order-field"
                    className="form-radio h-4 w-4 text-blue-600"
                    checked={value.field === f}
                    onChange={() => update('field', f)}
                  />
                  <span className="ml-2">{f}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
