'use client';
import React, { useState } from 'react';

interface CreatedRangeFilterProps {
  value: {
    createdAtStart?: string;
    createdAtEnd?: string;
  };
  onChange: (value: { createdAtStart?: string; createdAtEnd?: string }) => void;
}

export default function CreatedRangeFilter({
  value = {},
  onChange,
}: CreatedRangeFilterProps) {
  const [expanded, setExpanded] = useState(false);

  const handleChange = (field: 'createdAtStart' | 'createdAtEnd', val: string) => {
    onChange({ ...value, [field]: val });
  };

  return (
    <div className="border border-gray-200 rounded mb-4">
      <button
        type="button"
        onClick={() => setExpanded((e) => !e)}
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
      >
        <span className="font-medium">Date time</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${
            expanded ? '' : 'rotate-180'
          }`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {expanded && (
        <div className="p-4 text-sm text-gray-700 space-y-4">
          <div>
            <label className="block mb-1 font-medium text-gray-600">
              Created At Start
            </label>
            <input
              type="datetime-local"
              className="w-full border border-gray-300 rounded px-2 py-1"
              value={value.createdAtStart || ''}
              onChange={(e) => handleChange('createdAtStart', e.target.value)}
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-600">
              Created At End
            </label>
            <input
              type="datetime-local"
              className="w-full border border-gray-300 rounded px-2 py-1"
              value={value.createdAtEnd || ''}
              onChange={(e) => handleChange('createdAtEnd', e.target.value)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
