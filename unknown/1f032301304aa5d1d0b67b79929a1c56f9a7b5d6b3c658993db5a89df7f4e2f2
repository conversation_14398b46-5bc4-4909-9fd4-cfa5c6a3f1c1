@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Colors based on the design requirements */
  --color-primary-blue: #082d4f;
  --color-secondary-blue: #007bff;
  --color-teal: #30b4b4;
  --color-blue-border: #3b82f6;

  /* Branding colors */
  --color-brand-primary: #30b4b4;
  --color-brand-secondary: #2a9d9d;

  /* Background and foreground */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Font families */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom spacing for header */
  --header-top-height: 3rem; /* h-12 */
  --header-nav-height: 3.5rem; /* h-14 */

  /* ShadCN UI Variables */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);

  /* Border radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Base styles */

/* Set body background to match footer */
html,
body {
  background: linear-gradient(
    to right,
    var(--color-brand-primary),
    var(--color-brand-secondary)
  );
  min-height: 100vh;
}

/* Override main content background to white */
main {
  background: white;
  min-height: calc(
    100vh - 200px
  ); /* Adjust for fixed header + breadcrumb + footer */
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* Custom utility classes for RTL/LTR */
.rtl\:flex-row-reverse[dir="rtl"] {
  flex-direction: row-reverse;
}

.rtl\:text-right[dir="rtl"] {
  text-align: right;
}

.rtl\:text-left[dir="rtl"] {
  text-align: left;
}

.ltr\:text-left[dir="ltr"] {
  text-align: left;
}

.ltr\:text-right[dir="ltr"] {
  text-align: right;
}

/* Mobile Navigation Improvements */
@media (max-width: 768px) {
  .mobile-nav-slide-in {
    transform: translateX(0);
  }

  .mobile-nav-slide-out {
    transform: translateX(100%);
  }

  [dir="rtl"] .mobile-nav-slide-out {
    transform: translateX(-100%);
  }
}

/* Responsive Navigation */
.nav-scroll {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-scroll::-webkit-scrollbar {
  display: none;
}

/* RTL-aware animations */
[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

[dir="ltr"] .slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Logo responsive styles */
.logo-container {
  transition: all 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

/* Ensure logo maintains aspect ratio */
.logo-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* Custom ZATCA Brand Colors */
  --zatca-primary-start: #002447;
  --zatca-primary-end: #0f294b;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Line clamp utility for table descriptions */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth animations for expandable content */
.animate-in {
  animation-duration: 200ms;
  animation-fill-mode: both;
}

.slide-in-from-top-2 {
  animation-name: slideInFromTop;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced trend indicator animations */
@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

/* Trend indicator hover effects */
.trend-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.trend-indicator:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Trend indicator glow effects */
.trend-indicator.trend-up:hover {
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3), 0 0 20px rgba(34, 197, 94, 0.1);
}

.trend-indicator.trend-down:hover {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3), 0 0 20px rgba(239, 68, 68, 0.1);
}

.trend-indicator.trend-stable:hover {
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3),
    0 0 20px rgba(107, 114, 128, 0.1);
}

/* Enhanced card animations and effects */
@keyframes card-float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.005);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.card-float {
  animation: card-float 6s ease-in-out infinite;
}

.card-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

/* Premium glass morphism effect */
.glass-morphism {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

/* Enhanced hover states for cards */
.card-premium:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* UX-focused improvements */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Improved focus states for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Smooth loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Better hover states for interactive elements */
.interactive-hover {
  @apply transition-all duration-200 ease-out hover:scale-105 active:scale-95;
}

/* Improved text readability */
.text-balance {
  text-wrap: balance;
}

/* Card content spacing */
.card-content-spacing > * + * {
  margin-top: 1rem;
}

/* Smooth card animations matching reference design */
@keyframes card-hover {
  0% {
    transform: translateY(0) scale(1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.card-smooth-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-smooth-hover:hover {
  animation: card-hover 0.2s ease-out forwards;
}

/* Compact card layout optimizations */
.compact-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 100px;
  max-height: 120px;
}

/* Icon container smooth scaling */
.icon-smooth-scale {
  transition: transform 0.15s ease-out;
}

.icon-smooth-scale:hover {
  transform: scale(1.05);
}

/* Text content smooth transitions */
.text-smooth {
  transition: color 0.15s ease-out, opacity 0.15s ease-out;
}
