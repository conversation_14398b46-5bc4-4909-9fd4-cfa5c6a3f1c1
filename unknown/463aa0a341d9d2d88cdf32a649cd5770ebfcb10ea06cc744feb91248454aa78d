export interface TripDetail {
  id: string;
  tripNumber: string;
  transitType?: string;
  transitSeqNo?: string;
  status: 'planned' | 'active' | 'completed' | 'cancelled' | 'delayed';
  
  origin: Location;
  destination: Location;
  plannedStartTime: Date;
  actualStartTime?: Date;
  plannedEndTime: Date;
  actualEndTime?: Date;
  estimatedArrival?: Date;
  
  vehicle: Vehicle;
  driver: Driver;
  
  route: RoutePoint[];
  totalDistance: number;
  plannedDuration: number;
  actualDuration?: number;
  
  currentLocation?: Location;
  completedDistance: number;
  progressPercentage: number;
  trackingStatus?: string;
  
  events: TripEvent[];
  milestones: TripMilestone[];
  alerts: TripAlert[];
  incidents: TripIncident[];
  
  cargo?: CargoInfo;
  cost?: CostInfo;
  notes?: string;
  attachments?: Attachment[];
}

export interface Location {
  id?: string;
  name: string;
  latitude: number;
  longitude: number;
  address?: string;
  type?: 'port' | 'warehouse' | 'checkpoint' | 'other';
}

export interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  vehicleType: string;
  trackerId: string;
  trackerType?: string;
  elocks?: string;
}

export interface Driver {
  id: string;
  name: string;
  licenseNumber: string;
  nationality: string;
  contactNumber: string;
  passportNumber: string;
}

export interface RoutePoint {
  id: string;
  name?: string;
  latitude: number;
  longitude: number;
  order: number;
}

export interface TripEvent {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: Date;
  location?: Location;
}

export interface TripMilestone {
  id: string;
  name: string;
  description: string;
  plannedTime: Date;
  actualTime?: Date;
  status: 'pending' | 'completed' | 'delayed';
}

export interface TripAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  timestamp: Date;
  severity: 'high' | 'medium' | 'low';
  status: 'active' | 'acknowledged' | 'resolved';
  location?: Location;
}

export interface TripIncident {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: Date;
  severity: 'high' | 'medium' | 'low';
  status: 'open' | 'investigating' | 'resolved';
  reportedBy: string;
}

export interface CargoInfo {
  description: string;
  ownerDescription?: string;
  weight?: number;
  value?: number;
  type?: string;
}

export interface CostInfo {
  totalCost: number;
  currency: string;
  breakdown?: { [key: string]: number };
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  url: string;
  uploadedAt: Date;
}