'use client';
import React, { useState } from 'react';

interface TruckInfo {
  transitNo: string;
  seqNo: string;
  driverName: string;
  plate: string;
  tracker: string;
  tripCode: string;
  priority: string[]; // ['High', 'Medium', 'Low']
  status: string[];   // ['Active', 'Ended']
  location: string[]; // ['Target Onroute', 'Target in Exit Border', 'Target in Entry Border']
}

interface TruckInfoFilterProps {
  value: TruckInfo;
  onChange: (value: TruckInfo) => void;
}

export default function TruckInfoFilter({
  value = {
    transitNo: '',
    seqNo: '',
    driverName: '',
    plate: '',
    tracker: '',
    tripCode: '',
    priority: [],
    status: [],
    location: [],
  },
  onChange,
}: TruckInfoFilterProps) {
  const [expanded, setExpanded] = useState(false);

  // Helper to toggle in an array field
  const toggleArr = (field: keyof TruckInfo, val: string) => {
    const arr = value[field] as string[];
    const next = arr.includes(val) ? arr.filter((x) => x !== val) : [...arr, val];
    onChange({ ...value, [field]: next });
  };

  // Helper to update text fields
  const updateField = (field: keyof TruckInfo, v: string) => {
    onChange({ ...value, [field]: v });
  };

  return (
    <div className="border border-gray-200 rounded mb-4">
      {/* Header */}
      <button
        type="button"
        className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 rounded-t"
        onClick={() => setExpanded((v) => !v)}
      >
        <span className="font-medium">Truck Info</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${expanded ? '' : 'rotate-180'}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {expanded && (
        <div className="px-4 py-3 space-y-4">
          {/* Text inputs */}
          <div className="space-y-2">
            {[
              { label: 'Transit No', field: 'transitNo' as keyof TruckInfo },
              { label: 'Transit Seq No', field: 'seqNo' as keyof TruckInfo },
              { label: 'Driver Name', field: 'driverName' as keyof TruckInfo },
              { label: 'Plate No', field: 'plate' as keyof TruckInfo },
              { label: 'Tracker No', field: 'tracker' as keyof TruckInfo },
              { label: 'Trip Code', field: 'tripCode' as keyof TruckInfo },
            ].map(({ label, field }) => (
              <input
                key={field}
                type="text"
                placeholder={label}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                value={value[field]}
                onChange={(e) => updateField(field, e.target.value)}
              />
            ))}
          </div>

          {/* Trip Priority */}
          <div>
            <div className="font-medium text-sm mb-1">Trip Priority</div>
            <div className="flex space-x-4">
              {['High', 'Medium', 'Low'].map((opt) => (
                <label key={opt} className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-blue-600"
                    checked={value.priority.includes(opt)}
                    onChange={() => toggleArr('priority', opt)}
                  />
                  <span className="ml-2 text-sm">{opt}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Trip Status */}
          <div>
            <div className="font-medium text-sm mb-1">Trip Status</div>
            <div className="flex space-x-4">
              {['Active', 'Ended'].map((opt) => (
                <label key={opt} className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-blue-600"
                    checked={value.status.includes(opt)}
                    onChange={() => toggleArr('status', opt)}
                  />
                  <span className="ml-2 text-sm">{opt}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Trip Location */}
          <div>
            <div className="font-medium text-sm mb-1">Trip Location</div>
            <div className="flex flex-col space-y-2">
              {[
                'Target Onroute',
                'Target in Exit Border',
                'Target in Entry Border',
              ].map((opt) => (
                <label key={opt} className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-blue-600"
                    checked={value.location.includes(opt)}
                    onChange={() => toggleArr('location', opt)}
                  />
                  <span className="ml-2 text-sm">{opt}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
