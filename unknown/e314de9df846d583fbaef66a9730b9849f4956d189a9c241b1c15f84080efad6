"use client";

import React from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trip } from "@/types/trip";

interface TripDetailSummaryProps {
  trip?: Trip | null;
  className?: string;
}

export const TripDetailSummary: React.FC<TripDetailSummaryProps> = ({
  trip,
  className = "",
}) => {
  if (!trip) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="text-center text-gray-500">Trip not found</div>
        </CardContent>
      </Card>
    );
  }

  // Navigation icons configuration
  const navigationIcons = [
    { 
      id: 'trip_map', 
      icon: '🗺️', 
      title: 'Trip Map',
    },
    { 
      id: 'trip_alerts', 
      icon: '🔔', 
      title: 'Trip Alerts',
    },
    { 
      id: 'movement_report', 
      icon: '⚠️', 
      title: 'Movement Report',
    },
    { 
      id: 'trip_pings', 
      icon: '📊', 
      title: 'Trip Pings',
    },
    { 
      id: 'trip_activities_report', 
      icon: '✉️', 
      title: 'Activities Report',
    }
  ];

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <CardTitle className="text-xl font-bold">Trip Details</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4 gap-y-1 text-xs">
          {/* Row 1 */}
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Trip Code:</span>
            <span className="font-semibold">{trip.tripId}</span>
          </div>
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Transit:</span>
            <span className="font-semibold">{trip.transitNumber}</span>
          </div>

          {/* Row 2 */}
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Elocks:</span>
            <span className="font-semibold">{trip.tracking?.elocks || "N/A"}</span>
          </div>
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Tracker:</span>
            <span className="font-semibold text-right break-all">{trip.vehicle_details?.trackerNo || "N/A"}</span>
          </div>

          {/* Row 3 */}
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Distance:</span>
            <span className="font-semibold">
              {trip.tracking?.completeDistance ? `${trip.tracking.completeDistance} km` : "N/A"}
            </span>
          </div>
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Remaining:</span>
            <span className="font-semibold">
              {trip.tracking?.remainingDistance ? `${trip.tracking.remainingDistance} km` : "N/A"}
            </span>
          </div>

          {/* Row 4 */}
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">Arrival:</span>
            <span className="font-semibold text-right">{trip.expectedArrivalDate || "N/A"}</span>
          </div>
          <div className="flex justify-between py-1 border-b border-gray-100">
            <span className="text-gray-600 font-medium">End Date:</span>
            <span className="font-semibold">{trip.endDate || "N/A"}</span>
          </div>

          {/* Full width rows for longer content */}
          <div className="lg:col-span-2 py-1 border-b border-gray-100">
            <div className="flex flex-col gap-1">
              <span className="text-gray-600 font-medium">Owner:</span>
              <span className="font-semibold break-words text-sm">
                {trip.shipment?.ownerDescription || "N/A"}
              </span>
            </div>
          </div>

          <div className="lg:col-span-2 py-1 border-b border-gray-100">
            <div className="flex flex-col gap-1">
              <span className="text-gray-600 font-medium">Vehicle:</span>
              <span className="font-semibold break-words text-sm">
                {trip.vehicle_details ? `${trip.vehicle_details.vehiclePlateNumber || "N/A"}, ${trip.vehicle_details.model || "N/A"}, ${trip.vehicle_details.type || "N/A"}, ${trip.vehicle_details.plateCountry || "N/A"}` : "N/A"}
              </span>
            </div>
          </div>

          <div className="lg:col-span-2 py-1 border-b border-gray-100">
            <div className="flex flex-col gap-1">
              <span className="text-gray-600 font-medium">Driver:</span>
              <span className="font-semibold break-words text-sm">
                {trip.driver_details ? `${trip.driver_details.driverName || "N/A"}, ${trip.driver_details.driverNationality || "N/A"}` : "N/A"}
              </span>
            </div>
          </div>

          <div className="lg:col-span-2 py-1 border-b border-gray-100">
            <div className="flex flex-col gap-1">
              <span className="text-gray-600 font-medium">Security:</span>
              <span className="font-semibold break-words text-sm">{trip.compliance?.securityNotes || "N/A"}</span>
            </div>
          </div>

          <div className="lg:col-span-2 py-1">
            <div className="flex flex-col gap-1">
              <span className="text-gray-600 font-medium">Shipment:</span>
              <span className="font-semibold break-words text-sm">
                {trip.shipment?.shipmentDescription || "N/A"}
              </span>
            </div>
          </div>
        </div>

        {/* Alert Details Section */}
        <div className="mt-4">
          <h3 className="text-sm font-bold text-red-500 text-center mb-2">
            Alert Details
          </h3>
          <div className="text-center text-gray-500 py-2 text-xs">
            No Alerts
          </div>
        </div>

        {/* Navigation Icons */}
        <div className="mt-4 flex justify-center gap-2">
          {navigationIcons.map((nav) => (
            <Link
              key={nav.id}
              href={`/trip-details?trip_id=${trip.tripId}&current_tab=${nav.id}`}
              className={`w-8 h-8 rounded-md flex items-center justify-center text-white text-sm hover:opacity-80 transition-opacity bg-blue-500`}
              title={nav.title}
            >
              {nav.icon}
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
