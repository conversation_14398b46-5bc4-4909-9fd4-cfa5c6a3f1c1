import TripAlertMapViewer from "./TripAlertMapViewer";
import tripAlertsData from "../data/trip_alerts.json";
import { TripAlert } from "@/types/trip_alert";
import { TripDetailSummary } from '@/components/TripDetailSummary';
import { useTripData } from '@/hooks/useTripData';
import { Trip } from '@/types/trip';

interface TripAlertsData {
  tripAlerts: TripAlert[];
}

interface Props {
  row: Trip;
}

export default function TripMasterDetailView({ row }: Props) {
  const { tripData, loading, error } = useTripData(row.tripId);
  const tripAlerts = (tripAlertsData as unknown as TripAlertsData).tripAlerts;

  if (loading) {
    return (
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 text-center py-8">
          <div className="text-gray-500">Loading trip details...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 text-center py-8">
          <div className="text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
   <div className="matser-detail-view grid grid-cols-12 gap-4 p-2 ">
      
      {/* Left Sidebar / Detail Panel (3 cols) */}
      <div className="col-span-12 md:col-span-3">
        <div className="text-sm text-gray-600 space-y-1">
          <TripDetailSummary trip={tripData} />
        </div>
      </div>

      {/* Map Viewer (9 cols) */}
      <div className="col-span-12 md:col-span-9">
        <TripAlertMapViewer
          tripAlerts={tripAlerts}
          selectedTripId={row.tripId}
          className="h-[600px]"
        />
      </div>
    </div>
  
 
  );
}
