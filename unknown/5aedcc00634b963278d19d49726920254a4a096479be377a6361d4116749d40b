'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import SummaryCard from '@/components/SummaryCard';
import ClockCard from '@/components/shared/ClockComponent';
import TripAlertList from '@/components/AlertListView';

export default function  TripAlert() {
  const { t, dir } = useLanguage();
  const cardSize = "lg";
  const showTrends = false;
  const dashboardCards = [
    {
      id: "inactive-trips-count",
      titleKey: "dashboard.card.inactiveTrips",
      value: 1551,
      icon: "bell-off" as const,
      color: "gray" as const,
      trend: { value: 0, direction: "stable" as const },
    },
    {
      id: "resolved-trips-count",
      titleKey: "dashboard.card.resolvedToday",
      value: 1562,
      icon: "check" as const,
      color: "green" as const,
      trend: { value: 8.7, direction: "up" as const },
    },
    {
      id: "critical-alerts-count",
      titleKey: "dashboard.card.criticalAlerts",
      value: 0,
      icon: "prohibition" as const,
      color: "red" as const,
      trend: { value: 12.3, direction: "down" as const },
    },
    {
      id: "pending-reviews-count",
      titleKey: "dashboard.card.pendingAlerts",
      value: 847,
      icon: "bell-gray" as const,
      color: "gray" as const,
      trend: { value: 3.4, direction: "up" as const },
    },
    
  ];

  return (
   <div className="bg-gray-100 min-h-screen p-6 space-y-2 " dir={dir}>
   
         
           <div className="mb-4">
             <h2 className="text-xl font-semibold text-gray-900 mb-4">
               {/* {t("dummy.section.individualCards")} */}
             </h2>
             <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                <ClockCard variant="compact" showSettings={false} dateFormat='hijri' size={cardSize}/>

               {dashboardCards.slice(0, 5).map((card) => (
                
                 <SummaryCard
                   key={`individual-${card.id}`}
                   id={card.id}
                   titleKey={card.titleKey}
                   value={card.value}
                   icon={card.icon}
                   color={card.color}
                   size={cardSize}
                   showTrends={showTrends}
                   trend={card.trend}
                   className="w-full"
                 />
               ))}
             </div>
           </div>
            <TripAlertList/>
           
         </div>
  );
}
