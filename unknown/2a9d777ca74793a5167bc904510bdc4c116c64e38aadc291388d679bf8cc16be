import { TripDetail } from '../types/trip';

export const mockTripData: { [key: string]: TripDetail } = {
  'TRP001': {
    id: 'TRP001',
    tripNumber: '14471/01/20',
    transitType: 'Import Transit',
    transitSeqNo: 'V123',
    status: 'active',
    
    origin: {
      name: 'King <PERSON><PERSON>ad <PERSON>tl Airport',
      latitude: 26.2285,
      longitude: 50.1520,
      address: 'Dammam, Saudi Arabia'
    },
    destination: {
      name: 'Khalij Border',
      latitude: 25.4052,
      longitude: 49.9777,
      address: 'Eastern Province, Saudi Arabia'
    },
    
    plannedStartTime: new Date('2025-01-17T06:00:00Z'),
    actualStartTime: new Date('2025-01-17T06:15:00Z'),
    plannedEndTime: new Date('2025-01-17T18:00:00Z'),
    estimatedArrival: new Date('2025-01-17T18:30:00Z'),
    
    vehicle: {
      id: 'VEH001',
      plateNumber: 'ABC-********',
      make: 'Mercedes',
      model: 'Actros',
      year: 2022,
      vehicleType: 'Truck',
      trackerId: '16539133',
      elocks: '15627136'
    },
    
    driver: {
      id: 'DRV001',
      name: 'dimo',
      licenseNumber: 'LIC2024001',
      nationality: 'sudan',
      contactNumber: '966501568563',
      passportNumber: 'ABCD1234'
    },
    
    route: [
      { id: '1', latitude: 26.2285, longitude: 50.1520, order: 1 },
      { id: '2', latitude: 25.4052, longitude: 49.9777, order: 2 }
    ],
    
    totalDistance: 450,
    plannedDuration: 720, // 12 hours in minutes
    completedDistance: 180,
    progressPercentage: 40,
    trackingStatus: 'Activated • Target in Entry Border',
    
    currentLocation: {
      name: 'Highway 40',
      latitude: 25.8,
      longitude: 50.0
    },
    
    events: [],
    milestones: [],
    alerts: [],
    incidents: [],
    
    cargo: {
      description: 'ShipmentDescription',
      ownerDescription: 'OwnerDescription'
    }
  },
  
  'TRP002': {
    id: 'TRP002',
    tripNumber: '14472/02/20',
    transitType: 'Export Transit',
    transitSeqNo: 'V124',
    status: 'completed',
    
    origin: {
      name: 'Riyadh Central Hub',
      latitude: 24.7136,
      longitude: 46.6753
    },
    destination: {
      name: 'Kuwait Border Crossing',
      latitude: 29.0375,
      longitude: 46.5197
    },
    
    plannedStartTime: new Date('2025-01-16T08:00:00Z'),
    actualStartTime: new Date('2025-01-16T08:10:00Z'),
    plannedEndTime: new Date('2025-01-16T16:00:00Z'),
    actualEndTime: new Date('2025-01-16T15:45:00Z'),
    
    vehicle: {
      id: 'VEH002',
      plateNumber: 'XYZ-9876',
      make: 'Volvo',
      model: 'FH16',
      year: 2021,
      vehicleType: 'Truck',
      trackerId: '16539134'
    },
    
    driver: {
      id: 'DRV002',
      name: 'Mohammed Hassan',
      licenseNumber: 'LIC2024002',
      nationality: 'Egypt',
      contactNumber: '966507654321',
      passportNumber: 'EGY5678'
    },
    
    route: [
      { id: '1', latitude: 24.7136, longitude: 46.6753, order: 1 },
      { id: '2', latitude: 29.0375, longitude: 46.5197, order: 2 }
    ],
    
    totalDistance: 520,
    plannedDuration: 480,
    actualDuration: 455,
    completedDistance: 520,
    progressPercentage: 100,
    
    events: [],
    milestones: [],
    alerts: [],
    incidents: [],
    
    cargo: {
      description: 'Agricultural Products',
      ownerDescription: 'Al-Harbi Agricultural Exports'
    }
  },
  
  'TRP003': {
    id: 'TRP003',
    tripNumber: '14473/03/20',
    transitType: 'Import Transit',
    transitSeqNo: 'V125',
    status: 'delayed',
    
    origin: {
      name: 'Jeddah Islamic Port',
      latitude: 21.4858,
      longitude: 39.1925
    },
    destination: {
      name: 'Riyadh Distribution Center',
      latitude: 24.7136,
      longitude: 46.6753
    },
    
    plannedStartTime: new Date('2025-01-17T10:00:00Z'),
    actualStartTime: new Date('2025-01-17T11:30:00Z'),
    plannedEndTime: new Date('2025-01-17T22:00:00Z'),
    estimatedArrival: new Date('2025-01-18T01:00:00Z'),
    
    vehicle: {
      id: 'VEH003',
      plateNumber: 'DEF-5432',
      make: 'Scania',
      model: 'R450',
      year: 2020,
      vehicleType: 'Truck',
      trackerId: '16539135'
    },
    
    driver: {
      id: 'DRV003',
      name: 'Ali Al-Mahmoud',
      licenseNumber: 'LIC2024003',
      nationality: 'Saudi Arabia',
      contactNumber: '966501234567',
      passportNumber: 'SAU9012'
    },
    
    route: [
      { id: '1', latitude: 21.4858, longitude: 39.1925, order: 1 },
      { id: '2', latitude: 24.7136, longitude: 46.6753, order: 2 }
    ],
    
    totalDistance: 950,
    plannedDuration: 720,
    completedDistance: 200,
    progressPercentage: 21,
    
    events: [],
    milestones: [],
    alerts: [
      {
        id: 'ALT001',
        type: 'critical',
        title: 'Traffic Delay',
        description: 'Heavy traffic causing significant delay',
        timestamp: new Date(),
        severity: 'high',
        status: 'active',
        location: {
          name: 'Highway 40',
          latitude: 22.5,
          longitude: 40.5
        }
      }
    ],
    incidents: [],
    
    cargo: {
      description: 'Electronics and Consumer Goods',
      ownerDescription: 'Tech Import Co.'
    }
  }
};