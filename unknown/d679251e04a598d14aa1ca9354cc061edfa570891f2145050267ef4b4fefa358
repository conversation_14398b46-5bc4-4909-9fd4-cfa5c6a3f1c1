/**
 * Authentication Utility
 * Handles user login, session management, and authentication state
 */

import { validateUserCredentials } from "./mockUsers";
import {
  User,
  LoginCredentials,
  AuthResponse,
  UserSession,
} from "@/types/auth";
import {
  STORAGE_KEYS,
  SESSION_DURATION,
  AUTH_ERROR_CODES,
} from "@/constants/auth";

export type {
  User,
  LoginCredentials,
  AuthResponse,
  UserSession,
} from "@/types/auth";

export const authenticateUser = async (
  credentials: LoginCredentials
): Promise<AuthResponse> => {
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const { username, password } = credentials;

    if (!username?.trim() || !password?.trim()) {
      return {
        success: false,
        message: "Username and password are required",
        errorCode: AUTH_ERROR_CODES.INVALID_CREDENTIALS,
      };
    }

    const user = validateUserCredentials(username.trim(), password);

    if (!user) {
      return {
        success: false,
        message: "Invalid username or password",
        errorCode: AUTH_ERROR_CODES.INVALID_CREDENTIALS,
      };
    }

    if (user.status !== "active") {
      return {
        success: false,
        message: "Account is not active",
        errorCode: AUTH_ERROR_CODES.ACCOUNT_INACTIVE,
      };
    }

    const { password: _, ...userWithoutPassword } = user;

    return {
      success: true,
      user: userWithoutPassword,
      message: "Login successful",
    };
  } catch (error) {
    console.error("Authentication error:", error);
    return {
      success: false,
      message: "An error occurred during authentication",
      errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
    };
  }
};

export const storeUserSession = (
  user: Omit<User, "password">,
  rememberMe: boolean = false
): void => {
  // Check if we're in the browser
  if (typeof window === "undefined") return;

  try {
    const now = Date.now();
    const sessionData: UserSession = {
      user,
      timestamp: now,
      rememberMe,
      expiresAt:
        now +
        (rememberMe ? SESSION_DURATION.EXTENDED : SESSION_DURATION.REGULAR),
    };

    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(STORAGE_KEYS.USER_SESSION, JSON.stringify(sessionData));
  } catch (error) {
    console.error("Error storing user session:", error);
  }
};

export const getCurrentUser = (): Omit<User, "password"> | null => {
  // Check if we're in the browser
  if (typeof window === "undefined") return null;

  try {
    let sessionData = localStorage.getItem(STORAGE_KEYS.USER_SESSION);

    if (!sessionData) {
      sessionData = sessionStorage.getItem(STORAGE_KEYS.USER_SESSION);
    }

    if (sessionData) {
      const parsed: UserSession = JSON.parse(sessionData);
      const now = Date.now();
      const isExpired = now > parsed.expiresAt;

      if (!isExpired) {
        return parsed.user;
      } else {
        clearUserSession();
      }
    }
  } catch (error) {
    console.error("Error getting user session:", error);
    clearUserSession();
  }

  return null;
};

export const clearUserSession = (): void => {
  // Check if we're in the browser
  if (typeof window === "undefined") return;

  try {
    localStorage.removeItem(STORAGE_KEYS.USER_SESSION);
    sessionStorage.removeItem(STORAGE_KEYS.USER_SESSION);
  } catch (error) {
    console.error("Error clearing user session:", error);
  }
};

export const isAuthenticated = (): boolean => {
  return getCurrentUser() !== null;
};

export const logout = (): void => {
  clearUserSession();
  if (typeof window !== "undefined") {
    window.location.href = "/login";
  }
};
