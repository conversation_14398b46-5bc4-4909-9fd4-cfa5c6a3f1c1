[{"alert_type": "Tracker <PERSON>", "value": "10000"}, {"alert_type": "Tracker Dropped", "value": "10001"}, {"alert_type": "Lock Tamper", "value": "10002"}, {"alert_type": "Lock Open", "value": "10003"}, {"alert_type": "Lock Connection Lost", "value": "10004"}, {"alert_type": "Tracker Battery Low", "value": "10005"}, {"alert_type": "Lock Low Battery", "value": "10006"}, {"alert_type": "Lock Very Low Battery", "value": "10007"}, {"alert_type": "GSM Signal Lost", "value": "10008"}, {"alert_type": "GPS Signal Lost", "value": "10009"}, {"alert_type": "Geofence Entry Breach", "value": "10021"}, {"alert_type": "Geofence Exit Breach", "value": "10022"}, {"alert_type": "Tracker Connection Lost", "value": "10023"}, {"alert_type": "Trip Distance Exceeded", "value": "10024"}, {"alert_type": "Trip Time Exceeded", "value": "10025"}, {"alert_type": "Over Speeding", "value": "10040"}, {"alert_type": "Truck Stopped", "value": "10060"}, {"alert_type": "Wrong Direction", "value": "10061"}, {"alert_type": "Entry into Customs Area", "value": "10101"}, {"alert_type": "Truck Moved", "value": "10067"}, {"alert_type": "4 hours exceeded", "value": "10080"}, {"alert_type": "Suspected Area", "value": "10066"}]