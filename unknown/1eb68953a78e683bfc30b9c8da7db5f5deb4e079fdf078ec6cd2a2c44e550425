"use client";

import React, { useState } from "react";
import PageTemplate from "../../../../components/layout/PageTemplate";
import TripAlertMapViewer from "../../../../components/TripAlertMapViewer";
// import SimpleMapTest from "../../../../components/SimpleMapTest";
import tripAlertsData from "../../../../data/trip_alerts.json";
import { TripAlert } from "../../../../types/trip_alert";

const TripAlertMapPage: React.FC = () => {
  const [selectedTripId, setSelectedTripId] = useState<string | undefined>(undefined);
  const [selectedAlert, setSelectedAlert] = useState<TripAlert | null>(null);

  const tripAlerts = tripAlertsData.tripAlerts as TripAlert[];

  // Get unique trip IDs for trip filter
  const uniqueTripIds = Array.from(new Set(tripAlerts.map(alert => alert.tripId)));

  const handleAlertClick = (alert: TripAlert) => {
      setSelectedAlert(alert);
      console.log("Alert clicked:", alert);
    };

  const handleTripFilter = (tripId: string | undefined) => {
    setSelectedTripId(tripId);
    setSelectedAlert(null);
  };

  return (
    <PageTemplate
      titleKey="tripAlertMap.title"
      description="Interactive map showing trip alerts with filtering capabilities"
    >
      <div className="space-y-6">
        {/* Trip Filter Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Trip Filter
          </h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleTripFilter(undefined)}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                selectedTripId === undefined
                  ? "bg-blue-100 border-blue-300 text-blue-800"
                  : "bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100"
              }`}
            >
              All Trips ({tripAlerts.length})
            </button>
            {uniqueTripIds.map(tripId => {
              const tripAlertCount = tripAlerts.filter(alert => alert.tripId === tripId).length;
              return (
                <button
                  key={tripId}
                  onClick={() => handleTripFilter(tripId)}
                  className={`px-4 py-2 rounded-lg border transition-colors ${
                    selectedTripId === tripId
                      ? "bg-blue-100 border-blue-300 text-blue-800"
                      : "bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  {tripId} ({tripAlertCount})
                </button>
              );
            })}
          </div>
        </div>

        {/* Alert Details Section */}
        {selectedAlert && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Selected Alert Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Alert ID:</label>
                <p className="text-gray-800">{selectedAlert.alertId}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Type:</label>
                <p className="text-gray-800">{selectedAlert.alertType}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Severity:</label>
                <p className={`font-medium ${
                  selectedAlert.severity === 'critical' ? 'text-red-600' :
                  selectedAlert.severity === 'high' ? 'text-orange-600' :
                  selectedAlert.severity === 'medium' ? 'text-yellow-600' :
                  'text-green-600'
                }`}>
                  {selectedAlert.severity}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Status:</label>
                <p className="text-gray-800">{selectedAlert.alertStatus}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Trip ID:</label>
                <p className="text-gray-800">{selectedAlert.tripId}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Route:</label>
                <p className="text-gray-800">{selectedAlert.routeName}</p>
              </div>
              <div className="md:col-span-2 lg:col-span-3">
                <label className="text-sm font-medium text-gray-600">Description:</label>
                <p className="text-gray-800">{selectedAlert.description}</p>
              </div>
              <div className="md:col-span-2 lg:col-span-3">
                <label className="text-sm font-medium text-gray-600">Location:</label>
                <p className="text-gray-800">{selectedAlert.location.address}</p>
              </div>
            </div>
          </div>
        )}

        {/* Map Viewer Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <TripAlertMapViewer
            tripAlerts={tripAlerts}
            selectedTripId={selectedTripId}
            onAlertClick={handleAlertClick}
            className="h-[600px]"
          />
          {/* <SimpleMapTest /> */}
        </div>

        {/* Statistics Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Total Alerts</h4>
            <p className="text-2xl font-bold text-gray-800">
              {selectedTripId ? tripAlerts.filter(alert => alert.tripId === selectedTripId).length : tripAlerts.length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Critical Alerts</h4>
            <p className="text-2xl font-bold text-red-600">
              {(selectedTripId ? tripAlerts.filter(alert => alert.tripId === selectedTripId) : tripAlerts)
                .filter(alert => alert.severity === 'critical').length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Unresolved</h4>
            <p className="text-2xl font-bold text-orange-600">
              {(selectedTripId ? tripAlerts.filter(alert => alert.tripId === selectedTripId) : tripAlerts)
                .filter(alert => alert.alertStatus.includes('triggered')).length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Active Trips</h4>
            <p className="text-2xl font-bold text-blue-600">
              {selectedTripId ? 1 : uniqueTripIds.length}
            </p>
          </div>
        </div>
      </div>
    </PageTemplate>
  );
};

export default TripAlertMapPage;