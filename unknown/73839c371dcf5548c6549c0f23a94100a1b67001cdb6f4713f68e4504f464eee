'use client';

import { useLanguage } from '../../../contexts/LanguageContext';
import ClockCard from '@/components/shared/ClockComponent';
import { PageTemplate } from '../../../components/layout';

export default function ClockDemoPage() {
  const { language } = useLanguage();

  return (
    <PageTemplate 
      titleKey={language === 'ar' ? 'عرض مكون الساعة' : 'Clock Component Demo'}
      description={language === 'ar' ? 'عرض توضيحي لمكون الساعة مع التقويم الهجري والميلادي' : 'Demonstration of the Clock component with Hijri and Gregorian calendars'}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'مكون الساعة والتاريخ' : 'Clock & Date Component'}
          </h2>
          <p className="text-gray-600 mb-4">
            {language === 'ar' 
              ? 'مكون شامل يعرض الوقت والتاريخ بالتقويمين الهجري والميلادي مع خيارات متعددة للعرض والتخصيص.'
              : 'A comprehensive component that displays time and date in both Hijri and Gregorian calendars with multiple display and customization options.'
            }
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">
                {language === 'ar' ? 'الميزات:' : 'Features:'}
              </h4>
              <ul className="space-y-1 text-gray-600">
                <li>• {language === 'ar' ? 'عرض الوقت الحي' : 'Live time display'}</li>
                <li>• {language === 'ar' ? 'التقويم الهجري والميلادي' : 'Hijri & Gregorian calendars'}</li>
                <li>• {language === 'ar' ? 'تنسيقات متعددة للوقت (12/24 ساعة)' : 'Multiple time formats (12/24 hour)'}</li>
                <li>• {language === 'ar' ? 'دعم المناطق الزمنية المختلفة' : 'Multiple timezone support'}</li>
                <li>• {language === 'ar' ? 'أنماط عرض متنوعة' : 'Various display styles'}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">
                {language === 'ar' ? 'أنماط العرض:' : 'Display Styles:'}
              </h4>
              <ul className="space-y-1 text-gray-600">
                <li>• {language === 'ar' ? 'رقمي (افتراضي)' : 'Digital (default)'}</li>
                <li>• {language === 'ar' ? 'تناظري (ساعة دائرية)' : 'Analog (circular clock)'}</li>
                <li>• {language === 'ar' ? 'مبسط (مضغوط)' : 'Minimal (compact)'}</li>
              </ul>
            </div>
          </div>
        </div>
         <div className="space-y-12">
                {/* Default Clock  */}
              <div className='ml-4'>
                  <h3 className="text-2xl font-bold text-gray-800 mb-6">
                    {language === 'ar' ? 'الساعة الافتراضية' : 'Default Clock'}
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <ClockCard />
                    <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
                      <h4 className="text-lg font-semibold text-gray-800 mb-2">
                        {language === 'ar' ? 'الوصف:' : 'Description:'}
                      </h4>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {language === 'ar'
                          ? 'الساعة الافتراضية تعرض الوقت الحالي مع التاريخ بالتقويمين الهجري والميلادي. تتضمن إعدادات قابلة للتخصيص لتنسيق الوقت والتاريخ.'
                          : 'The default clock displays current time with both Hijri and Gregorian dates. Includes customizable settings for time and date formats.'}
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <p className="text-base text-gray-700 font-medium">
                    {language === 'ar'
                      ? 'تتوفر أيضًا نسختان بديلتان لعرض الساعة حسب احتياجك:'
                      : 'Two additional display variants are available depending on your needs:'}
                  </p>
                </div>

              {/* Compact Variant */}
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">
                  {language === 'ar' ? 'النسخة المضغوطة' : 'Compact Variant'}
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                  <ClockCard variant="compact" />
                  <ClockCard variant="compact" showSettings={false} />
                  <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">
                      {language === 'ar' ? 'الوصف:' : 'Description:'}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {language === 'ar'
                        ? 'النسخة المضغوطة مناسبة للمساحات الصغيرة. يمكن إخفاء إعدادات التخصيص عند الحاجة.'
                        : 'Compact variant suitable for smaller spaces. Customization settings can be hidden when needed.'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Detailed Variant */}
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">
                  {language === 'ar' ? 'النسخة المفصلة' : 'Detailed Variant'}
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                  <ClockCard variant="detailed" />
                  <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
                    <h4 className=" text-lg font-semibold text-gray-800 mb-2">
                      {language === 'ar' ? 'الوصف:' : 'Description:'}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {language === 'ar'
                        ? 'النسخة المفصلة توفر مساحة أكبر لعرض المعلومات مع تصميم أكثر وضوحاً وتفصيلاً.'
                        : 'Detailed variant provides more space for information display with a clearer and more detailed design.'
                      }
                    </p>
                  </div>
                </div>
              </div>
         </div>


        {/* Divider */}
        <hr className="my-16 border-gray-300" />
         {/* Extra Examples Section */}
        <section>
          <h2 className="text-3xl font-bold text-gray-800 mb-10">
            {language === 'ar' ? 'تجارب إضافية على الكروت' : 'Additional Clock Card Examples'}
          </h2>

          {/* UI Control Examples */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-gray-700 mb-6 border-b pb-2">
              {language === 'ar' ? 'خيارات التحكم في الواجهة' : 'UI Control Options'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              
              {/* Timezone Control Disabled */}
              <div className="bg-white border rounded-xl p-5 shadow-sm hover:shadow-md transition">
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  🕒 {language === 'ar' ? 'بدون تحكم في المنطقة الزمنية' : 'No Timezone Selector'}
                </h4>
                <p className="text-gray-600 text-sm mb-4">
                  {language === 'ar'
                    ? 'المنطقة الزمنية غير متاحة للمستخدم'
                    : 'Timezone selector is disabled for the user.'}
                </p>
                <ClockCard variant="compact" enableTimezoneControl={false} />
              </div>

              {/* Limited Controls */}
              <div className="bg-white border rounded-xl p-5 shadow-sm hover:shadow-md transition">
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  ⚙️ {language === 'ar' ? 'تحكم محدود' : 'Limited Controls'}
                </h4>
                <p className="text-gray-600 text-sm mb-4">
                  {language === 'ar'
                    ? 'فقط إعدادات التاريخ والوقت متاحة'
                    : 'Only time/date format is available.'}
                </p>
                <ClockCard
                  variant="compact"
                  enableTimezoneControl={false}
                  enableDisplayStyleControl={false}
                />
              </div>

              {/* No Settings at All */}
              <div className="bg-white border rounded-xl p-5 shadow-sm hover:shadow-md transition">
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  🚫 {language === 'ar' ? 'بدون إعدادات' : 'No Settings'}
                </h4>
                <p className="text-gray-600 text-sm mb-4">
                  {language === 'ar'
                    ? 'عرض الساعة فقط بدون أي خيارات'
                    : 'Clock display only with no controls.'}
                </p>
                <ClockCard variant="compact" showSettings={false} />
              </div>
            </div>
          </div>

          {/* Timezone Examples */}
          <div>
            <h3 className="text-2xl font-semibold text-gray-700 mb-6 border-b pb-2">
              {language === 'ar' ? 'أمثلة المناطق الزمنية' : 'Timezone Variants'}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              
              {/* Riyadh Default */}
              <div className="bg-white border rounded-xl p-5 shadow-sm hover:shadow-md transition">
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  🇸🇦 {language === 'ar' ? 'الرياض (افتراضي)' : 'Riyadh (Default)'}
                </h4>
                <p className="text-gray-600 text-sm mb-4">
                  {language === 'ar'
                    ? 'افتراضي المملكة العربية السعودية'
                    : 'Default for Saudi Arabia'}
                </p>
                <ClockCard variant="compact" showSettings={false}  />
              </div>

              {/* Customizable */}
              <div className="bg-white border rounded-xl p-5 shadow-sm hover:shadow-md transition">
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  🛠️ {language === 'ar' ? 'مع إعدادات مخصصة' : 'With Custom Settings'}
                </h4>
                <p className="text-gray-600 text-sm mb-4">
                  {language === 'ar'
                    ? 'يمكن للمستخدم اختيار المنطقة الزمنية'
                    : 'User can change timezone settings.'}
                </p>
                <ClockCard variant="compact" />
              </div>

              {/* Supported Timezones List */}
              <div className="bg-white border rounded-xl p-5 shadow-sm hover:shadow-md transition">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">
                  🌐 {language === 'ar' ? 'المناطق الزمنية المدعومة' : 'Supported Timezones'}
                </h4>
                <ul className="text-sm text-gray-600 space-y-2 list-disc list-inside">
                  <li>{language === 'ar' ? 'الرياض، الكويت، الدوحة' : 'Riyadh, Kuwait, Doha'}</li>
                  <li>{language === 'ar' ? 'دبي، المنامة' : 'Dubai, Manama'}</li>
                  <li>{language === 'ar' ? 'لندن، نيويورك' : 'London, New York'}</li>
                  <li>{language === 'ar' ? 'طوكيو، سيدني' : 'Tokyo, Sydney'}</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>
    </PageTemplate>
  );
}