export interface TripAlert {
  alertId: string;
  alertType: string;
  alertStatus: string[];
  transitNumber:string; // Added transitNumber
  tripId: string;
  transitSequence: number;
  routeName: string;
  shipmentDescription: string;
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  severity: string;
  description: string;
  acknowledgedBy?: string | null;
  acknowledgedAt?: string | null;
  resolvedBy?: string | null;
  resolvedAt?: string | null;
  comments?: string;
  relatedData: {
    speed: number;
    batteryLevel: number;
    signalStrength: number;
    geofenceId: string | null;
  };
}