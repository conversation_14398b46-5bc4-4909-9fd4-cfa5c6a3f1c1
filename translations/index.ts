import { en } from "./en";
import { ar } from "./ar";

export type Language = "en" | "ar";

export type TranslationKey = keyof typeof en;

export const translations = {
  en,
  ar,
} as const;

export { en, ar };


/**
 * Convert a translation key to smart text when translation is not found
 * Example: "dashboard.card.inactiveTrips" -> "Inactive Trips"
 */
const keyToSmartText = (key: string): string => {
  // Split by dots and take the last part (the actual key)
  const parts = key.split('.');
  const lastPart = parts[parts.length - 1];
  
  // Convert camelCase to space-separated words
  const words = lastPart
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
    .trim();
  
  return words;
};

/**
 * Get translation for a specific key
 * If translation is not found, returns smart text derived from the key
 */
export const getTranslation = (language: Language, key: string): string => {
  // const keys = key.split(".");
  const languageTranslations = translations[language] as Record<string, string>;

  // for (const k of keys) {
  //   value = value?.[k];
  // }
  const value = languageTranslations?.[key];
  
  // If translation exists, return it
  if (value) {
    return value;
  }
  
  // If translation doesn't exist, return smart text
  return keyToSmartText(key);
};

/**
 * Get direction for language (RTL for Arabic, LTR for English)
 */
export const getDirection = (language: Language): "rtl" | "ltr" => {
  return language === "ar" ? "rtl" : "ltr";
};

/**
 * Get text alignment for language
 */
export const getTextAlign = (language: Language): "right" | "left" => {
  return language === "ar" ? "right" : "left";
};

/**
 * Get flex direction for language (reverse for Arabic)
 */
export const getFlexDirection = (language: Language): "row" | "row-reverse" => {
  return language === "ar" ? "row-reverse" : "row";
};
