export const ar = {
  "header.language": "العربية",
  "header.languageSwitch": "English",
  "header.userAccount": "أحمد علي",
  "header.profile": "الملف الشخصي",
  "header.settings": "الإعدادات",
  "header.logout": "تسجيل الخروج",
  "header.agencyName": "هيئة الزكاة والضريبة والجمارك",
  "header.agencyNameArabic": "Zakat, Tax, Customs Authority",
  "navigation.locationMonitor": "مراقب الموقع",
  "navigation.focusedTrips": "الرحلات المركزة",
  "navigation.myAssignedPorts": "الموانئ المخصصة لي",
  "navigation.dashboard": "لوحة التحكم",
  "navigation.configuration": "الإعدادات",
  "navigation.suspiciousTrips": "الرحلات المشبوهة",
  "navigation.reports": "التقارير",
  "navigation.demoTrip": "رحلة تجريبية",
  "navigation.demo": "العرض التوضيحي",
  "navigation.listDemo": "عرض القائمة",
  "navigation.mapDemo": "عرض الخريطة",
  "navigation.ahmedTest": "اختبار أحمد",
  "footer.poweredBy": "مدعوم من",
  "footer.technicalUnited": "لابلاس سوفتوير",
  "footer.technicalUnitedArabic": "Laplacesoftware",

  // Demo Trip Page
  'demoTrip.title': 'إدارة الرحلات التجريبية',
  'demoTrip.description': 'نظام شامل لتتبع وإدارة الرحلات',
  'demoTrip.backToTable': 'العودة إلى جدول الرحلات',
  'demoTrip.tripDetails': 'تفاصيل الرحلة',
  'demoTrip.tripNumber': 'رقم الرحلة',
  'demoTrip.status': 'الحالة',
  'demoTrip.type': 'النوع',
  'demoTrip.driver': 'السائق',
  'demoTrip.vehicle': 'المركبة',
  'demoTrip.route': 'المسار',
  'demoTrip.shipment': 'الشحنة',
  'demoTrip.tracking': 'التتبع',
  'demoTrip.compliance': 'الامتثال',
  'demoTrip.moreInfo': 'المزيد من المعلومات',
  'demoTrip.progress': 'التقدم',
  'demoTrip.estimatedArrival': 'الوصول المتوقع',
  'demoTrip.creationDate': 'تاريخ الإنشاء',
  'demoTrip.activationDate': 'تاريخ التفعيل',
  'demoTrip.completionDate': 'تاريخ الإكمال',

  // Trip Alert Map Page
  'tripAlertMap.title': 'عارض خريطة تنبيهات الرحلة'
} as const;
