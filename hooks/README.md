# Custom Hooks Documentation

## Overview

This directory contains custom React hooks that provide state management, data handling, and utility functions for the Interactive Map system. Each hook is designed with specific responsibilities and follows React best practices for performance and maintainability.

## Hook Architecture

```
Custom Hooks System
├── useSidebarState.ts (UI State Management)
├── useMapState.ts (Google Maps Integration)
├── useMapData.ts (Static Data Loading)
├── usePageData.ts (Dynamic Data Management)
└── useLanguage.ts (Language Detection & Management)
```

---

## 1. useSidebarState.ts

### Purpose

Manages the state of the interactive map sidebar, including tab navigation, filters, and user interactions.

### Main Hook: `useSidebarState()`

**Responsibilities:**

- Sidebar open/close state management
- Active tab tracking (search, alerts, routes)
- Search and route filter state management
- Filter reset functionality

**State Structure:**

```typescript
interface UseSidebarState {
  isOpen: boolean;
  activeTab: SidebarTab; // 'search' | 'alerts' | 'routes'
  searchFilters: SearchFilters;
  routeFilters: RouteFilters;
  setIsOpen: (open: boolean) => void;
  setActiveTab: (tab: SidebarTab) => void;
  updateSearchFilters: (filters: Partial<SearchFilters>) => void;
  updateRouteFilters: (filters: Partial<RouteFilters>) => void;
  resetFilters: () => void;
}
```

**Initial State:**

```typescript
// Search Filters Default
const initialSearchFilters = {
  showPort: true, // Show port-type locations
  showCheckpost: true, // Show checkpoint-type locations
  tripCode: "Trip Code", // Default trip code filter
  searchValue: "", // Search input value
};

// Route Filters Default
const initialRouteFilters = {
  filter: "my-routes", // 'my-routes' | 'all'
  selectedRoutes: [], // Array of selected route IDs
  searchTerm: "", // Route search term
};
```

### Additional Hooks

#### `useRouteSelection(initialRoutes)`

**Purpose:** Manages route selection with search functionality

**Features:**

- Add/remove routes from selection
- Search term management with suggestions
- Clear all routes functionality
- Set routes programmatically

**Usage Example:**

```typescript
const {
  selectedRoutes,
  searchTerm,
  showSuggestions,
  addRoute,
  removeRoute,
  clearRoutes,
} = useRouteSelection(["route-1", "route-2"]);
```

#### `useAlertFilters()`

**Purpose:** Manages alert filtering state

**Features:**

- Severity filtering (high, medium, low)
- Alert type filtering
- Read/unread status filtering
- Reset all filters

---

## 2. useMapState.ts

### Purpose

Handles Google Maps API integration, map initialization, and marker management.

### Main Hook: `useMapState(initialCenter, initialZoom)`

**Responsibilities:**

- Google Maps API script loading
- Map instance creation and management
- Map center and zoom state
- Marker array management
- Cleanup and memory management

**Parameters:**

- `initialCenter`: MapCenter object with lat/lng coordinates
- `initialZoom`: Initial zoom level (default: 6)

**Return Value:**

```typescript
interface UseMapState {
  map: google.maps.Map | null;
  markers: google.maps.Marker[];
  isLoaded: boolean;
  center: MapCenter;
  zoom: number;
  setCenter: (center: MapCenter) => void;
  setZoom: (zoom: number) => void;
  mapRef: React.RefObject<HTMLDivElement>;
}
```

**Key Features:**

1. **Dynamic API Loading:** Loads Google Maps API script only when needed
2. **Environment Integration:** Uses `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`
3. **Error Handling:** Graceful handling of API loading failures
4. **Memory Management:** Proper cleanup of script elements

### Additional Hook: `useMapMarkers(map, locations, onLocationClick)`

**Purpose:** Creates and manages map markers with custom icons

**Features:**

- **Custom SVG Markers:** Creates location-type specific icons
- **Info Windows:** Interactive popups with location details
- **Event Handling:** Click events with callback support
- **Automatic Cleanup:** Removes old markers when locations change

**Marker Types & Colors:**

```typescript
const markerTypes = {
  airport: "#10b981", // Green - Airplane icon
  seaport: "#0ea5e9", // Blue - Ship icon
  landport: "#3b82f6", // Blue - Truck icon
  police_station: "#ef4444", // Red - Shield icon
  checkpoint: "#f59e0b", // Orange - Lock icon
};
```

**Marker Creation Process:**

1. Generate SVG icon based on location type
2. Create Google Maps marker with custom icon
3. Attach info window with location details
4. Add click event listener
5. Return marker instance for management

---

## 3. useMapData.ts

### Purpose

Manages static data loading from JSON files with caching and filtering capabilities.

### Main Hook: `useMapData()`

**Responsibilities:**

- Load default map data from JSON files
- Implement data caching for performance
- Provide loading and error states
- Offer data refetch functionality

**Data Sources:**

- `locations.json`: Default location data
- `alerts.json`: Default alert data
- `routes.json`: Default route data
- `statistics.json`: Default statistics data

**Caching Strategy:**

```typescript
// Global cache to prevent re-loading
let dataCache: {
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
} | null = null;
```

**Error Handling:**

- Graceful fallback to empty arrays/objects
- Detailed error logging
- User-friendly error messages
- Maintains app stability during data failures

### Filtering Hooks

#### `useFilteredLocations(locations, showPort, showCheckpost, searchValue)`

**Purpose:** Filters locations based on type and search criteria

**Filter Logic:**

1. **Type Filter:** Include/exclude ports and checkpoints
2. **Search Filter:** Match against name, nameAr, description, descriptionAr
3. **Case Insensitive:** Supports both English and Arabic search

#### `useFilteredAlerts(alerts, severity, type, isRead)`

**Purpose:** Filters alerts based on multiple criteria

**Filter Options:**

- **Severity:** 'high', 'medium', 'low'
- **Type:** Custom alert types
- **Read Status:** true/false/undefined

#### `useFilteredRoutes(routes, filter, searchTerm)`

**Purpose:** Filters routes based on ownership and search

**Filter Types:**

- **'my-routes':** Only user-owned routes (isUserRoute: true)
- **'all':** All available routes
- **Search:** Name and nameAr matching

---

## 4. usePageData.ts

### Purpose

Advanced data management system that loads different datasets based on page context.

### Main Hook: `usePageData(pageType)`

**Supported Page Types:**

- `'default'`: Standard map data
- `'location-monitor'`: Security monitoring data
- `'focused-trips'`: Priority trip data
- `'dummy-ahmed'`: Test data for development

**Dynamic Data Loading:**

```typescript
// Page-specific data files
const dataFiles = {
  "location-monitor": locationMonitorData,
  "focused-trips": focusedTripsData,
  "dummy-ahmed": dummyAhmedData,
  default: defaultData,
};
```

**Advanced Features:**

1. **Page-Specific Caching:** Separate cache for each page type
2. **Fallback Values:** Safe defaults for missing data
3. **Type Safety:** Full TypeScript support
4. **Error Boundaries:** Comprehensive error handling

### Helper Functions

#### `getPageTypeFromPath(pathname)`

**Purpose:** Automatically detect page type from URL

**Detection Logic:**

```typescript
if (pathname.includes("location-monitor")) return "location-monitor";
if (pathname.includes("focused-trips")) return "focused-trips";
if (pathname.includes("dummy/ahmed")) return "dummy-ahmed";
return "default";
```

#### `useAutoPageData()`

**Purpose:** Automatically load data based on current URL

**Usage:**

```typescript
// Automatically detects page type and loads appropriate data
const mapData = useAutoPageData();
```

---

## 5. useLanguage.ts

### Purpose

Comprehensive language detection and management system supporting Arabic (RTL) and English (LTR).

### Main Hook: `useLanguage()`

**Language Detection Priority:**

1. **URL Parameters:** `?lang=ar` or `?lang=en`
2. **localStorage:** Persistent user preference
3. **Document Direction:** RTL/LTR attribute
4. **Document Language:** HTML lang attribute
5. **Browser Language:** Navigator language settings
6. **Content Analysis:** Arabic Unicode character detection
7. **Default:** Falls back to English

**Real-time Monitoring:**

- **Storage Events:** Responds to localStorage changes
- **DOM Mutations:** Watches for lang/dir attribute changes
- **Cross-tab Sync:** Language changes sync across browser tabs

### Additional Hook: `useLanguageSetter()`

**Purpose:** Provides language switching functionality

**Features:**

- Updates localStorage for persistence
- Sets document lang and dir attributes
- Dispatches storage events for cross-component sync
- Handles proper locale codes (ar-SA, en-US)

**Usage Example:**

```typescript
const setLanguage = useLanguageSetter();

// Switch to Arabic
setLanguage("ar");
// - Sets localStorage: 'language' = 'ar'
// - Sets document.documentElement.lang = 'ar-SA'
// - Sets document.documentElement.dir = 'rtl'
// - Dispatches storage event for other components
```

---

## Performance Optimizations

### 1. Memoization

- **useMemo:** Expensive calculations cached
- **useCallback:** Event handlers optimized
- **Dependency Arrays:** Minimal re-renders

### 2. Caching Strategies

- **Global Caches:** Prevent redundant data loading
- **Page-Specific Caches:** Efficient multi-page support
- **Marker Caching:** Reuse marker instances when possible

### 3. Memory Management

- **Cleanup Functions:** Proper event listener removal
- **Marker Disposal:** Google Maps marker cleanup
- **Observer Disconnection:** MutationObserver cleanup

### 4. Error Boundaries

- **Graceful Degradation:** App continues functioning during errors
- **Fallback Data:** Safe defaults for all data types
- **Error Logging:** Detailed error information for debugging

## Usage Patterns

### Basic Implementation

```typescript
// Simple sidebar management
const { isOpen, setIsOpen, activeTab, setActiveTab } = useSidebarState();

// Map with markers
const { map, isLoaded } = useMapState();
const markers = useMapMarkers(map, locations, handleLocationClick);

// Data loading
const { locations, alerts, loading, error } = usePageData("location-monitor");

// Language support
const language = useLanguage();
```

### Advanced Implementation

```typescript
// Complete map system
const InteractiveMap = () => {
  const { language } = useLanguage();
  const { locations, alerts, routes, statistics } =
    usePageData("focused-trips");
  const { isOpen, activeTab, searchFilters } = useSidebarState();
  const { map, isLoaded } = useMapState();

  const filteredLocations = useFilteredLocations(
    locations,
    searchFilters.showPort,
    searchFilters.showCheckpost,
    searchFilters.searchValue
  );

  const markers = useMapMarkers(map, filteredLocations, handleLocationClick);

  return <div>{/* Map and sidebar components */}</div>;
};
```

## Advanced Implementation Examples

### Custom Hook Composition

```typescript
// Create a composite hook for complete map functionality
const useInteractiveMap = (pageType: PageType) => {
  const language = useLanguage();
  const mapData = usePageData(pageType);
  const sidebarState = useSidebarState();
  const mapState = useMapState();

  // Combine filtered data
  const filteredData = useMemo(
    () => ({
      locations: useFilteredLocations(
        mapData.locations,
        sidebarState.searchFilters.showPort,
        sidebarState.searchFilters.showCheckpost,
        sidebarState.searchFilters.searchValue
      ),
      alerts: useFilteredAlerts(mapData.alerts),
      routes: useFilteredRoutes(
        mapData.routes,
        sidebarState.routeFilters.filter,
        sidebarState.routeFilters.searchTerm
      ),
    }),
    [mapData, sidebarState]
  );

  return {
    ...mapData,
    ...sidebarState,
    ...mapState,
    language,
    filteredData,
  };
};
```

### Error Handling Patterns

```typescript
// Comprehensive error handling in hooks
const useRobustMapData = (pageType: PageType) => {
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  const { data, error, loading, refetch } = usePageData(pageType);

  const retryWithBackoff = useCallback(async () => {
    if (retryCount < maxRetries) {
      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
      setTimeout(() => {
        setRetryCount((prev) => prev + 1);
        refetch();
      }, delay);
    }
  }, [retryCount, refetch]);

  useEffect(() => {
    if (error && retryCount < maxRetries) {
      retryWithBackoff();
    }
  }, [error, retryWithBackoff]);

  return {
    ...data,
    error: retryCount >= maxRetries ? error : null,
    loading,
    canRetry: retryCount < maxRetries,
    retry: retryWithBackoff,
  };
};
```

### Performance Monitoring

```typescript
// Hook for monitoring performance metrics
const usePerformanceMetrics = () => {
  const [metrics, setMetrics] = useState({
    mapLoadTime: 0,
    dataLoadTime: 0,
    markerRenderTime: 0,
    totalMemoryUsage: 0,
  });

  const measureMapLoad = useCallback((startTime: number) => {
    const loadTime = performance.now() - startTime;
    setMetrics((prev) => ({ ...prev, mapLoadTime: loadTime }));
  }, []);

  const measureDataLoad = useCallback((startTime: number) => {
    const loadTime = performance.now() - startTime;
    setMetrics((prev) => ({ ...prev, dataLoadTime: loadTime }));
  }, []);

  const measureMarkerRender = useCallback((startTime: number) => {
    const renderTime = performance.now() - startTime;
    setMetrics((prev) => ({ ...prev, markerRenderTime: renderTime }));
  }, []);

  const measureMemoryUsage = useCallback(() => {
    if ("memory" in performance) {
      const memory = (performance as any).memory;
      setMetrics((prev) => ({
        ...prev,
        totalMemoryUsage: memory.usedJSHeapSize,
      }));
    }
  }, []);

  return {
    metrics,
    measureMapLoad,
    measureDataLoad,
    measureMarkerRender,
    measureMemoryUsage,
  };
};
```

## Testing Strategies

### Unit Testing Hooks

```typescript
// Example test for useSidebarState
import { renderHook, act } from "@testing-library/react";
import { useSidebarState } from "./useSidebarState";

describe("useSidebarState", () => {
  it("should initialize with default values", () => {
    const { result } = renderHook(() => useSidebarState());

    expect(result.current.isOpen).toBe(false);
    expect(result.current.activeTab).toBe("search");
    expect(result.current.searchFilters.showPort).toBe(true);
  });

  it("should toggle sidebar state", () => {
    const { result } = renderHook(() => useSidebarState());

    act(() => {
      result.current.setIsOpen(true);
    });

    expect(result.current.isOpen).toBe(true);
  });

  it("should update search filters", () => {
    const { result } = renderHook(() => useSidebarState());

    act(() => {
      result.current.updateSearchFilters({ showPort: false });
    });

    expect(result.current.searchFilters.showPort).toBe(false);
  });
});
```

### Integration Testing

```typescript
// Test hook interactions
describe("Hook Integration", () => {
  it("should coordinate between map and sidebar state", () => {
    const { result: sidebarResult } = renderHook(() => useSidebarState());
    const { result: mapResult } = renderHook(() =>
      useMapState({ lat: 24.7136, lng: 46.6753 }, 6)
    );

    // Test that sidebar changes affect map display
    act(() => {
      sidebarResult.current.updateSearchFilters({ searchValue: "airport" });
    });

    // Verify map responds to filter changes
    expect(sidebarResult.current.searchFilters.searchValue).toBe("airport");
  });
});
```

## Debugging and Development Tools

### Debug Hook

```typescript
// Development-only debugging hook
const useDebugInfo = (hookName: string, values: any) => {
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.group(`🔍 ${hookName} Debug Info`);
      console.log("Values:", values);
      console.log("Timestamp:", new Date().toISOString());
      console.groupEnd();
    }
  }, [hookName, values]);
};

// Usage in other hooks
const useSidebarState = () => {
  const state = {
    /* ... */
  };
  useDebugInfo("useSidebarState", state);
  return state;
};
```

### Performance Profiler

```typescript
// Hook for profiling performance
const useProfiler = (name: string) => {
  const startTime = useRef<number>();

  const start = useCallback(() => {
    startTime.current = performance.now();
  }, []);

  const end = useCallback(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }
  }, [name]);

  return { start, end };
};
```

## Best Practices and Guidelines

### 1. Hook Design Principles

- **Single Responsibility:** Each hook has one clear purpose
- **Composability:** Hooks can be combined for complex functionality
- **Reusability:** Generic enough for multiple use cases
- **Performance:** Optimized with proper memoization

### 2. State Management

- **Local State:** Use useState for component-specific state
- **Shared State:** Use context or external state management
- **Derived State:** Use useMemo for computed values
- **Side Effects:** Use useEffect with proper dependencies

### 3. Error Handling

- **Graceful Degradation:** App continues functioning during errors
- **User Feedback:** Clear error messages for users
- **Developer Tools:** Detailed logging for debugging
- **Recovery Mechanisms:** Retry logic and fallback states

### 4. Performance Optimization

- **Memoization:** Cache expensive calculations
- **Debouncing:** Limit frequent operations
- **Cleanup:** Proper resource disposal
- **Lazy Loading:** Load data only when needed

This comprehensive hook system provides a robust, scalable, and maintainable foundation for the Interactive Map application with excellent performance and developer experience.
