/**
 * @fileoverview Sidebar State Management Hook
 * @description Custom hook for managing the interactive map sidebar state, including
 * tab navigation, search filters, route filters, and user interactions.
 *
 * @features
 * - Sidebar open/close state management
 * - Active tab tracking (search, alerts, routes)
 * - Search filters for location types and search terms
 * - Route filters for user routes vs all routes
 * - Filter reset functionality
 * - Type-safe state management with TypeScript
 *
 * @usage
 * ```tsx
 * const {
 *   isOpen,
 *   activeTab,
 *   searchFilters,
 *   routeFilters,
 *   setIsOpen,
 *   setActiveTab,
 *   updateSearchFilters,
 *   updateRouteFilters,
 *   resetFilters
 * } = useSidebarState();
 * ```
 *
 * <AUTHOR> Map Team
 * @version 1.0.0
 * @since 2024-01-15
 */

import { useState } from "react";
import {
  SidebarTab,
  SearchFilters,
  RouteFilters,
  UseSidebarState,
} from "../types/map";

/**
 * Default search filters configuration
 * @description Initial state for location search filters
 * - showPort: Include port-type locations (airports, seaports, landports)
 * - showCheckpost: Include checkpoint-type locations (checkpoints, police stations)
 * - tripCode: Default trip code filter value
 * - searchValue: Empty search input value
 */
const initialSearchFilters: SearchFilters = {
  showPort: true,
  showCheckpost: true,
  tripCode: "Trip Code",
  searchValue: "",
};

/**
 * Default route filters configuration
 * @description Initial state for route filtering
 * - filter: Default to show only user's routes
 * - selectedRoutes: Empty array of selected route IDs
 * - searchTerm: Empty route search term
 */
const initialRouteFilters: RouteFilters = {
  filter: "my-routes",
  selectedRoutes: [],
  searchTerm: "",
};

/**
 * Main sidebar state management hook
 * @description Manages all aspects of the interactive map sidebar including
 * visibility, active tab, and filter states
 *
 * @returns {UseSidebarState} Object containing all sidebar state and actions
 *
 * @example
 * ```tsx
 * const {
 *   isOpen,
 *   setIsOpen,
 *   activeTab,
 *   setActiveTab,
 *   searchFilters,
 *   updateSearchFilters
 * } = useSidebarState();
 *
 * // Toggle sidebar
 * setIsOpen(!isOpen);
 *
 * // Switch to alerts tab
 * setActiveTab('alerts');
 *
 * // Update search filters
 * updateSearchFilters({ showPort: false, searchValue: 'airport' });
 * ```
 */
export const useSidebarState = (): UseSidebarState => {
  // Sidebar visibility state - starts closed by default
  const [isOpen, setIsOpen] = useState(false);

  // Active tab state - starts with search tab
  const [activeTab, setActiveTab] = useState<SidebarTab>("search");

  // Search filters state - manages location filtering
  const [searchFilters, setSearchFilters] =
    useState<SearchFilters>(initialSearchFilters);

  // Route filters state - manages route filtering and selection
  const [routeFilters, setRouteFilters] =
    useState<RouteFilters>(initialRouteFilters);

  /**
   * Updates search filters with partial updates
   * @param filters - Partial SearchFilters object to merge with current state
   * @description Merges new filter values with existing ones using spread operator
   */
  const updateSearchFilters = (filters: Partial<SearchFilters>) => {
    setSearchFilters((prev) => ({ ...prev, ...filters }));
  };

  /**
   * Updates route filters with partial updates
   * @param filters - Partial RouteFilters object to merge with current state
   * @description Merges new filter values with existing ones using spread operator
   */
  const updateRouteFilters = (filters: Partial<RouteFilters>) => {
    setRouteFilters((prev) => ({ ...prev, ...filters }));
  };

  /**
   * Resets all filters to their initial state
   * @description Useful for clearing all user-applied filters at once
   */
  const resetFilters = () => {
    setSearchFilters(initialSearchFilters);
    setRouteFilters(initialRouteFilters);
  };

  return {
    isOpen,
    activeTab,
    searchFilters,
    routeFilters,
    setIsOpen,
    setActiveTab,
    updateSearchFilters,
    updateRouteFilters,
    resetFilters,
  };
};

// Hook for managing route selection
export const useRouteSelection = (initialRoutes: string[] = []) => {
  const [selectedRoutes, setSelectedRoutes] = useState<string[]>(initialRoutes);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);

  const addRoute = (route: string) => {
    if (!selectedRoutes.includes(route)) {
      setSelectedRoutes((prev) => [...prev, route]);
    }
    setSearchTerm("");
    setShowSuggestions(false);
  };

  const removeRoute = (index: number) => {
    setSelectedRoutes((prev) => prev.filter((_, i) => i !== index));
  };

  const clearRoutes = () => {
    setSelectedRoutes([]);
  };

  const setRoutes = (routes: string[]) => {
    setSelectedRoutes(routes);
  };

  return {
    selectedRoutes,
    searchTerm,
    showSuggestions,
    setSearchTerm,
    setShowSuggestions,
    addRoute,
    removeRoute,
    clearRoutes,
    setRoutes,
  };
};

// Hook for managing alert filters
export const useAlertFilters = () => {
  const [severityFilter, setSeverityFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [readFilter, setReadFilter] = useState<boolean | undefined>(undefined);

  const resetAlertFilters = () => {
    setSeverityFilter("");
    setTypeFilter("");
    setReadFilter(undefined);
  };

  return {
    severityFilter,
    typeFilter,
    readFilter,
    setSeverityFilter,
    setTypeFilter,
    setReadFilter,
    resetAlertFilters,
  };
};
