import { useEffect, useState } from 'react';
import { Trip } from '@/types/trip';

export function useTripData(tripId?: string) {
  const [tripData, setTripData] = useState<Trip | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTripData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch from the unified trips.json
        const response = await fetch('/data/trips.json');
        if (!response.ok) {
          throw new Error('Failed to fetch trip data');
        }
        const data: Trip[] = await response.json();
        
        if (tripId) {
          // Find specific trip
          const trip = data.find((t: Trip) => t.tripId === tripId);
          if (!trip) {
            throw new Error(`Trip with ID ${tripId} not found`);
          }
          setTripData(trip);
        } else {
          // Return first trip if no ID specified
          const firstTrip = data[0];
          if (!firstTrip) {
            throw new Error('No trips available');
          }
          setTripData(firstTrip);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
        setTripData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchTripData();
  }, [tripId]);

  return { tripData, loading, error };
}
