/**
 * @fileoverview Language Detection and Management Hook
 * @description Comprehensive language detection system supporting Arabic (RTL) and English (LTR)
 * with multiple detection sources and real-time monitoring
 *
 * @features
 * - Multi-source language detection (URL, localStorage, DOM, browser, content)
 * - Real-time monitoring of language changes
 * - Cross-tab synchronization via storage events
 * - DOM mutation observation for dynamic changes
 * - RTL/LTR direction support
 * - Fallback mechanisms for reliable detection
 * - Language setting functionality with persistence
 *
 * @usage
 * ```tsx
 * // Basic language detection
 * const language = useLanguage();
 *
 * // Language setting
 * const setLanguage = useLanguageSetter();
 * setLanguage('ar'); // Switch to Arabic
 *
 * // Conditional rendering based on language
 * return (
 *   <div dir={language === 'ar' ? 'rtl' : 'ltr'}>
 *     {language === 'ar' ? 'مرحبا' : 'Hello'}
 *   </div>
 * );
 * ```
 *
 * <AUTHOR> Map Team
 * @version 1.0.0
 * @since 2024-01-15
 */

import { useState, useEffect } from "react";

export type Language = "en" | "ar";

/**
 * Main language detection hook
 * @description Detects and monitors the current language from multiple sources
 *
 * @returns {Language} Current detected language ('en' | 'ar')
 *
 * @example
 * ```tsx
 * const language = useLanguage();
 *
 * // Use in components
 * const greeting = language === 'ar' ? 'مرحبا' : 'Hello';
 * const direction = language === 'ar' ? 'rtl' : 'ltr';
 *
 * // Conditional styling
 * const textAlign = language === 'ar' ? 'text-right' : 'text-left';
 * ```
 *
 * @features
 * - Detects language from 6 different sources in priority order
 * - Monitors real-time changes via storage and DOM events
 * - Automatic cleanup of event listeners
 * - SSR-safe implementation
 */
export const useLanguage = (): Language => {
  // Language state with English as default
  const [language, setLanguage] = useState<Language>("en");

  useEffect(() => {
    /**
     * Comprehensive language detection function
     * @description Checks multiple sources in priority order to determine the current language
     * @returns {Language} Detected language ('en' | 'ar')
     *
     * Detection Priority:
     * 1. URL parameters (?lang=ar or ?lang=en) - Highest priority
     * 2. localStorage - User's saved preference
     * 3. Document direction (RTL/LTR) - Layout-based detection
     * 4. Document lang attribute - HTML lang attribute
     * 5. Browser language - System/browser preference
     * 6. Content analysis - Arabic Unicode character detection
     * 7. Default fallback - English as final fallback
     */
    const detectLanguage = (): Language => {
      // 1. Check URL parameters (highest priority)
      // Example: ?lang=ar or ?lang=en
      const urlParams = new URLSearchParams(window.location.search);
      const urlLang = urlParams.get("lang");
      if (urlLang === "ar" || urlLang === "en") {
        return urlLang as Language;
      }

      // 2. Check localStorage (user preference)
      // Persistent user choice from previous sessions
      const storedLang = localStorage.getItem("language");
      if (storedLang === "ar" || storedLang === "en") {
        return storedLang as Language;
      }

      // 3. Check document direction (RTL/LTR)
      // If document is set to RTL, assume Arabic
      const docDir = document.documentElement.dir;
      if (docDir === "rtl") {
        return "ar";
      }

      // 4. Check document lang attribute
      // HTML lang attribute like <html lang="ar-SA">
      const docLang = document.documentElement.lang;
      if (docLang.startsWith("ar")) {
        return "ar";
      }

      // 5. Check browser language settings
      // System or browser language preference
      const browserLang = navigator.language || navigator.languages?.[0];
      if (browserLang?.startsWith("ar")) {
        return "ar";
      }

      // 6. Check for Arabic content in the page
      const hasArabicContent =
        document.body.textContent?.match(/[\u0600-\u06FF]/);
      if (hasArabicContent) {
        return "ar";
      }

      // Default to English
      return "en";
    };

    const detectedLanguage = detectLanguage();
    setLanguage(detectedLanguage);

    // Listen for language changes in localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (
        e.key === "language" &&
        (e.newValue === "ar" || e.newValue === "en")
      ) {
        setLanguage(e.newValue as Language);
      }
    };

    // Listen for document direction changes
    const handleDirectionChange = () => {
      const newLang = document.documentElement.dir === "rtl" ? "ar" : "en";
      setLanguage(newLang);
    };

    // Listen for document lang attribute changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          (mutation.attributeName === "lang" ||
            mutation.attributeName === "dir")
        ) {
          const newLang = detectLanguage();
          setLanguage(newLang);
        }
      });
    });

    window.addEventListener("storage", handleStorageChange);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["lang", "dir"],
    });

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      observer.disconnect();
    };
  }, []);

  return language;
};

/**
 * Hook for setting and changing language programmatically
 * @description Provides a function to change the application language with full persistence
 * and cross-component synchronization
 *
 * @returns {Function} setLanguage function that accepts a Language parameter
 *
 * @example
 * ```tsx
 * const setLanguage = useLanguageSetter();
 *
 * // Language switcher component
 * const LanguageSwitcher = () => (
 *   <div>
 *     <button onClick={() => setLanguage('en')}>English</button>
 *     <button onClick={() => setLanguage('ar')}>العربية</button>
 *   </div>
 * );
 *
 * // Programmatic language change
 * useEffect(() => {
 *   if (userPreference === 'arabic') {
 *     setLanguage('ar');
 *   }
 * }, [userPreference]);
 * ```
 *
 * @features
 * - Persistent storage in localStorage
 * - Updates document lang and dir attributes
 * - Cross-tab synchronization via storage events
 * - Proper locale codes (ar-SA, en-US)
 * - Immediate UI updates across all components
 */
export const useLanguageSetter = () => {
  /**
   * Sets the application language with full persistence and synchronization
   * @param lang - Language to set ('en' | 'ar')
   *
   * @description Performs the following actions:
   * 1. Saves language preference to localStorage
   * 2. Updates document.documentElement.lang with proper locale
   * 3. Updates document.documentElement.dir for RTL/LTR layout
   * 4. Dispatches storage event for cross-component synchronization
   */
  const setLanguage = (lang: Language) => {
    // 1. Update localStorage for persistence across sessions
    localStorage.setItem("language", lang);

    // 2. Update document lang attribute with proper locale codes
    document.documentElement.lang = lang === "ar" ? "ar-SA" : "en-US";

    // 3. Update document direction for proper RTL/LTR layout
    document.documentElement.dir = lang === "ar" ? "rtl" : "ltr";

    // 4. Dispatch storage event for cross-component synchronization
    window.dispatchEvent(
      new StorageEvent("storage", {
        key: "language",
        newValue: lang,
        oldValue: localStorage.getItem("language"),
      })
    );
  };

  return setLanguage;
};
