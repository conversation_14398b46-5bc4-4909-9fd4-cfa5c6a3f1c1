/**
 * @fileoverview Map Data Management Hook
 * @description Custom hook for loading and managing static map data from JSON files
 * with caching, filtering capabilities, and error handling
 *
 * @features
 * - Static data loading from JSON files (locations, alerts, routes, statistics)
 * - Global data caching to prevent unnecessary re-loads
 * - Loading and error state management
 * - Data refetch functionality with cache invalidation
 * - Filtering hooks for locations, alerts, and routes
 * - Type-safe data structures with fallback values
 * - Performance optimization with useMemo
 *
 * @usage
 * ```tsx
 * // Basic data loading
 * const { locations, alerts, routes, statistics, loading, error } = useMapData();
 *
 * // Filtered locations
 * const filteredLocations = useFilteredLocations(
 *   locations,
 *   true,  // showPort
 *   false, // showCheckpost
 *   'airport' // searchValue
 * );
 *
 * // Filtered alerts by severity
 * const highAlerts = useFilteredAlerts(alerts, 'high');
 *
 * // User routes only
 * const myRoutes = useFilteredRoutes(routes, 'my-routes', '');
 * ```
 *
 * <AUTHOR> Map Team
 * @version 1.0.0
 * @since 2024-01-15
 */

import { useState, useEffect, useMemo } from "react";
import {
  Location,
  Alert,
  Route,
  TripStatistics,
  UseMapData,
} from "../types/map";

// Import JSON data files
import locationsData from "../data/locations.json";
import alertsData from "../data/alerts.json";
import routesData from "../data/routes.json";
import statisticsData from "../data/statistics.json";

/**
 * Global cache for loaded data to prevent re-loading
 * @description Stores loaded data in memory to avoid repeated JSON parsing
 * and improve performance across component re-renders
 */
let dataCache: {
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
} | null = null;

/**
 * Main map data loading hook
 * @description Loads static map data from JSON files with caching and error handling
 *
 * @returns {UseMapData} Object containing data arrays, loading state, error state, and refetch function
 *
 * @example
 * ```tsx
 * const {
 *   locations,
 *   alerts,
 *   routes,
 *   statistics,
 *   loading,
 *   error,
 *   refetch
 * } = useMapData();
 *
 * // Handle loading state
 * if (loading) return <LoadingSpinner />;
 *
 * // Handle error state
 * if (error) return <ErrorMessage error={error} onRetry={refetch} />;
 *
 * // Use data
 * return <MapComponent locations={locations} alerts={alerts} />;
 * ```
 */
export const useMapData = (): UseMapData => {
  // Loading state - true if no cached data exists
  const [loading, setLoading] = useState(!dataCache);

  // Error state for data loading failures
  const [error, setError] = useState<string | null>(null);

  // Use cached data if available, otherwise load from JSON
  const data = useMemo(() => {
    if (dataCache) {
      return dataCache;
    }

    try {
      const newData = {
        locations: locationsData.locations as Location[],
        alerts: alertsData.alerts as Alert[],
        routes: routesData.routes as Route[],
        statistics: statisticsData.tripStatistics as TripStatistics,
      };

      dataCache = newData;
      return newData;
    } catch (err) {
      console.error("Error loading map data:", err);
      setError(err instanceof Error ? err.message : "Failed to load data");
      return {
        locations: [],
        alerts: [],
        routes: [],
        statistics: {
          pieData: [],
          barData: [],
          summary: {
            totalActiveTrips: 0,
            totalCompletedTrips: 0,
            totalAlerts: 0,
            communicationLostTrips: 0,
            averageDelay: 0,
            onTimePercentage: 0,
            lastUpdated: new Date().toISOString(),
          },
        },
      };
    }
  }, []);

  const refetch = async () => {
    // Clear cache and reload
    dataCache = null;
    setLoading(true);
    setError(null);

    // Small delay to show loading state
    await new Promise((resolve) => setTimeout(resolve, 100));

    try {
      const newData = {
        locations: locationsData.locations as Location[],
        alerts: alertsData.alerts as Alert[],
        routes: routesData.routes as Route[],
        statistics: statisticsData.tripStatistics as TripStatistics,
      };

      dataCache = newData;
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to reload data");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!dataCache) {
      // Small delay to show loading state on first load
      const timer = setTimeout(() => {
        setLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setLoading(false);
    }
  }, []);

  return {
    locations: data.locations,
    alerts: data.alerts,
    routes: data.routes,
    statistics: data.statistics,
    loading,
    error,
    refetch,
  };
};

/**
 * Hook for filtering locations based on type and search criteria
 * @description Filters location array based on location type toggles and search input
 *
 * @param locations - Array of all available locations
 * @param showPort - Whether to include port-type locations (airport, seaport, landport)
 * @param showCheckpost - Whether to include checkpoint-type locations (checkpoint, police_station)
 * @param searchValue - Search term to match against location names and descriptions
 * @returns Filtered array of locations matching the criteria
 *
 * @example
 * ```tsx
 * // Show only airports and seaports containing "international"
 * const filteredLocations = useFilteredLocations(
 *   allLocations,
 *   true,  // showPort = true
 *   false, // showCheckpost = false
 *   'international' // searchValue
 * );
 * ```
 *
 * @features
 * - Type-based filtering (ports vs checkpoints)
 * - Multi-language search (English and Arabic)
 * - Case-insensitive search
 * - Searches in name, nameAr, description, and descriptionAr fields
 * - Optimized with useMemo for performance
 */
export const useFilteredLocations = (
  locations: Location[],
  showPort: boolean,
  showCheckpost: boolean,
  searchValue: string
) => {
  return useMemo(() => {
    return locations.filter((location) => {
      // Filter by location type
      const typeFilter =
        (showPort &&
          ["airport", "seaport", "landport"].includes(location.type)) ||
        (showCheckpost &&
          ["checkpoint", "police_station"].includes(location.type));

      // Skip if location type doesn't match filter
      if (!typeFilter) return false;

      // Apply search filter if search value is provided
      if (searchValue.trim()) {
        const searchLower = searchValue.toLowerCase();
        return (
          location.name.toLowerCase().includes(searchLower) ||
          location.nameAr.includes(searchValue) ||
          location.description?.toLowerCase().includes(searchLower) ||
          location.descriptionAr?.includes(searchValue)
        );
      }

      return true;
    });
  }, [locations, showPort, showCheckpost, searchValue]);
};

/**
 * Hook for filtering alerts based on multiple criteria
 * @description Filters alert array based on severity, type, and read status
 *
 * @param alerts - Array of all available alerts
 * @param severity - Optional severity filter ('high', 'medium', 'low')
 * @param type - Optional alert type filter
 * @param isRead - Optional read status filter (true/false/undefined)
 * @returns Filtered array of alerts matching all provided criteria
 *
 * @example
 * ```tsx
 * // Get only high severity unread alerts
 * const criticalAlerts = useFilteredAlerts(
 *   allAlerts,
 *   'high',    // severity
 *   undefined, // any type
 *   false      // unread only
 * );
 *
 * // Get all communication alerts regardless of read status
 * const commAlerts = useFilteredAlerts(allAlerts, undefined, 'communication');
 * ```
 */
export const useFilteredAlerts = (
  alerts: Alert[],
  severity?: string,
  type?: string,
  isRead?: boolean
) => {
  return useMemo(() => {
    return alerts.filter((alert) => {
      // Filter by severity if specified
      if (severity && alert.severity !== severity) return false;

      // Filter by type if specified
      if (type && alert.type !== type) return false;

      // Filter by read status if specified
      if (isRead !== undefined && alert.isRead !== isRead) return false;

      return true;
    });
  }, [alerts, severity, type, isRead]);
};

/**
 * Hook for filtering routes based on ownership and search criteria
 * @description Filters route array based on user ownership and search terms
 *
 * @param routes - Array of all available routes
 * @param filter - Route ownership filter ('my-routes' | 'all')
 * @param searchTerm - Search term to match against route names
 * @returns Filtered array of routes matching the criteria
 *
 * @example
 * ```tsx
 * // Get only user's routes containing "patrol"
 * const myPatrolRoutes = useFilteredRoutes(
 *   allRoutes,
 *   'my-routes', // only user routes
 *   'patrol'     // search term
 * );
 *
 * // Get all routes (no ownership filter)
 * const allRoutes = useFilteredRoutes(routes, 'all', '');
 * ```
 */
export const useFilteredRoutes = (
  routes: Route[],
  filter: "my-routes" | "all",
  searchTerm: string
) => {
  return useMemo(() => {
    let filteredRoutes = routes;

    // Apply ownership filter
    if (filter === "my-routes") {
      filteredRoutes = routes.filter((route) => route.isUserRoute);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filteredRoutes = filteredRoutes.filter(
        (route) =>
          route.name.toLowerCase().includes(searchLower) ||
          route.nameAr.includes(searchTerm)
      );
    }

    return filteredRoutes;
  }, [routes, filter, searchTerm]);
};
